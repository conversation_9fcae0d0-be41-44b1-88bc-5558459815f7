<?php

namespace app\admin\model\work;

use think\Model;
use traits\model\SoftDelete;

class Order extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'work_order';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'delivery_time_text',
        'status_text'
    ];
    

    
    public function getStatusList()
    {
        return ['pending' => __('Pending'), 'processing' => __('Processing'), 'completed' => __('Completed'), 'cancelled' => __('Cancelled')];
    }


    public function getDeliveryTimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['delivery_time'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['status'] ?? '');
        $list = $this->getStatusList();
        return $list[$value] ?? '';
    }

    protected function setDeliveryTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


}
