<?php
return [
    'Department'                                            => '部门',
    'Principal'                                             => '负责部门',
    'Login time'                                            => '最后登录',
    'You can select multiple departments to assign the person in charge. If you do not join the Department, you will join'=> '可选择负责多个部门，如果未加入该部门会加入',
    'Parent'                                                => '上级',
    'Loginfailure'                                          => '登录失败次数',
    "Department can't null"                                 => '部门不能为空',
    'Organizational'                                        => '组织架构',
    'Employee'                                              => '部门成员',
    'Department list'                                       => '部门管理',
    'Principal set'                                         => '设置负责部门',
    'Mobile'                                                => '手机',
    'Email'                                                 => '邮箱',
];
