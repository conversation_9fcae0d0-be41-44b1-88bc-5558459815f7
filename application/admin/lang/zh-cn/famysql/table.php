<?php

return [
    'Copy 1'                         => '复制表结构',
    'Copy 2'                         => '复制表结构及数据',
    'Truncate'                       => '清空数据',
    'Optimize'                       => '优化表',
    'Repair'                         => '修复表',
    'Table Operate'                  => '表操作',
    'Optimize table %s done'         => '优化表[%s]成功',
    'Repair table %s done'           => '修复表[%s]成功',
    'Truncate table %s done'         => '清空表[%s]成功',
    'Copy table %s done'             => '复制表[%s]成功',
    'Optimize table %s fail'         => '优化表[%s]失败',
    'Repair table %s fail'           => '修复表[%s]失败',
    'Truncate table %s fail'         => '清空表[%s]失败',
    'Copy table %s fail'             => '复制表[%s]失败',
    'Backup and Restore'             => '备份与还原',
    'Backup now'                     => '立即备份',
    'File'                           => '文件',
    'Size'                           => '大小',
    'Date'                           => '备份日期',
    'Delete successful'              => '删除成功',
    'Backup successful'              => '备份成功',
    'Restore successful'             => '还原成功',
    'Can not open zip file'          => '无法打开备份文件',
    'Can not unzip file'             => '无法解压备份文件',
    'Invalid parameters'             => '无效参数',
    'File not found'                 => '找不到文件',
    'Download completed'             => '下载完成',
    'Zip tips 1'                     => '服务器缺少php-zip组件，无法进行还原操作',
    'Sql file not found'             => '找不到sql文件',
    'Zip tips 2'                     => '服务器缺少php-zip组件，无法进行备份操作',
    'Uploaded successful'            => '上传成功',
    'Batch_add'                      => '快速建表',
    'Addon'                          => '插件',
    'Name'                           => '表名称',
    'Engine'                         => '引擎',
    'Charset'                        => '字符集',
    'Collation'                      => '排序规则',
    'Comment'                        => '注释',
    'Comment placeholder'            => '注释',
    'Name placeholder'               => '表名称(不包含表前缀及插件名，例article,结果为：表前缀+插件名+article)',
    'Copy'                           => '复制表',
    'More Table Operate'             => '更多表操作',
    'Index manager'                  => '索引管理',
    'Field manager'                  => '字段管理',
    'Download file'                  => '下载文件',
    'Rows'                           => '行数',
];                