<?php

namespace app\admin\controller\import;

use app\common\controller\Backend;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use think\Config;
use think\Db;
use think\Exception;
use app\admin\controller\import\ImportHelper;

/**
 * Excel数据处理器
 * 负责读取和解析Excel/CSV文件数据
 */
class ExcelDataProcessor extends Backend
{
    /**
     * 处理导入文件数据
     * @param string $path 文件路径
     * @param int $startRow 开始行
     * @param string $table 目标表
     * @param string $headType 标题类型(comment/name)
     * @param string $newTable 新表名
     * @return array
     * @throws Exception
     */
    public function process($path, $startRow, $table, $headType, $newTable = '')
    {
        $filePath = ROOT_PATH . DS . 'public' . DS . $path;
        if (!is_file($filePath)) {
            throw new Exception(__('No results were found'));
        }

        // 获取文件扩展名并实例化对应的reader
        $ext = pathinfo($filePath, PATHINFO_EXTENSION);
        $reader = $this->getReader($ext, $filePath);

        // 处理表字段信息
        $prefix = Config::get('database.prefix');
        $database = Config::get('database.database');
        list($fieldArr, $notnull) = $this->getTableFields($table, $newTable, $headType, $prefix, $database);

        // 读取并处理Excel数据
        return $this->readExcelData($reader, $filePath, $startRow, $fieldArr, $headType, $path);
    }

    /**
     * 获取合适的文件读取器
     * @param string $ext 文件扩展名
     * @param string $filePath 文件路径
     * @return Xlsx|Xls|Csv
     * @throws Exception
     */
    protected function getReader($ext, $filePath)
    {
        switch ($ext) {
            case 'csv':
                $this->preprocessCsvFile($filePath);
                return new Csv();
            case 'xls':
                return new Xls();
            case 'xlsx':
                return new Xlsx();
            default:
                throw new Exception(__('Unknown data format'));
        }
    }

    /**
     * 预处理CSV文件
     * @param string $filePath 文件路径
     */
    protected function preprocessCsvFile(&$filePath)
    {
        $file = fopen($filePath, 'r');
        $tempPath = tempnam(sys_get_temp_dir(), 'import_csv');
        $fp = fopen($tempPath, "w");
        $n = 0;
        
        while ($line = fgets($file)) {
            $line = rtrim($line, "\n\r\0");
            $encoding = mb_detect_encoding($line, ['utf-8', 'gbk', 'latin1', 'big5']);
            if ($encoding != 'utf-8') {
                $line = mb_convert_encoding($line, 'utf-8', $encoding);
            }
            if ($n == 0 || preg_match('/^".*"$/', $line)) {
                fwrite($fp, $line . "\n");
            } else {
                fwrite($fp, '"' . str_replace(['"', ','], ['""', '","'], $line) . "\"\n");
            }
            $n++;
        }
        
        fclose($file);
        fclose($fp);
        $filePath = $tempPath;
    }

    /**
     * 获取表字段信息
     * @param string $table 表名
     * @param string $newTable 新表名
     * @param string $headType 标题类型
     * @param string $prefix 表前缀
     * @param string $database 数据库名
     * @return array
     */
    protected function getTableFields($table, $newTable, $headType, $prefix, $database)
    {
        $fieldArr = [];
        $notnull = [];
        
        if (empty($newTable)) {
            $pk = Db::getTableInfo($table, 'pk');
            $list = Db::query(
                "SELECT COLUMN_NAME,COLUMN_COMMENT,COLUMN_TYPE,IS_NULLABLE FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = ? AND TABLE_SCHEMA = ?",
                [$table, $database]
            );
            
            foreach ($list as $v) {
                if ($v['COLUMN_NAME'] !== $pk) {
                    $fieldName = ($headType == 'comment' && $v['COLUMN_COMMENT']) 
                        ? $v['COLUMN_COMMENT'] 
                        : $v['COLUMN_NAME'];
                    
                    $fieldArr[$fieldName] = $v;
                    if ($v['IS_NULLABLE'] === 'NO') {
                        $notnull[] = $fieldName;
                    }
                }
            }
        }
        
        return [$fieldArr, $notnull];
    }

    /**
     * 读取Excel数据
     * @param object $reader 读取器对象
     * @param string $filePath 文件路径
     * @param int $startRow 开始行
     * @param array $fieldArr 字段数组
     * @param string $headType 标题类型
     * @param string $originalPath 原始文件路径
     * @return array
     * @throws Exception
     */
    protected function readExcelData($reader, $filePath, $startRow, $fieldArr, $headType, $originalPath)
    {
        $helper = new ImportHelper();
        
        try {
            $spreadsheet = $reader->load($filePath);
            $sheet = $spreadsheet->getSheet(0);
            
            $highestColumn = $sheet->getHighestDataColumn();
            $highestRow = $sheet->getHighestRow();
            $maxColumn = Coordinate::columnIndexFromString($highestColumn);
            
            // 读取标题行
            $fields = [];
            $col = [];
            $count = 0;
            
            for ($colNum = 1; $colNum <= $maxColumn; $colNum++) {
                $val = $sheet->getCellByColumnAndRow($colNum, 1)->getValue();
                $fields[] = $val;
                
                $col[] = [
                    'title' => $val,
                    'class' => isset($fieldArr[$val]) ? 'success' : '-',
                    'type' => isset($fieldArr[$val]) ? $fieldArr[$val]['COLUMN_TYPE'] : '--',
                    'field' => $val,
                    'fieldName' => isset($fieldArr[$val]) ? $fieldArr[$val]['COLUMN_NAME'] : '--'
                ];
                
                if (isset($fieldArr[$val])) {
                    $count++;
                }
            }
            
            // 读取数据行
            $allData = [];
            $insert = [];
            
            for ($rowNum = $startRow; $rowNum <= $highestRow; $rowNum++) {
                $values = [];
                for ($colNum = 1; $colNum <= $maxColumn; $colNum++) {
                    $values[] = $sheet->getCellByColumnAndRow($colNum, $rowNum)->getValue() ?? '';
                }
                
                $rowData = array_combine($fields, $values);
                $processedRow = $helper->processRowData($rowData, $fieldArr);
                
                if (!empty($processedRow['rows'])) {
                    $insert[] = $processedRow['rows'];
                }
                $allData[] = $processedRow['all'];
            }
            
            return [
                'path' => $originalPath,
                'field' => $col,
                'fieldArr' => $fieldArr,
                'data' => $allData,
                'insert' => $insert,
                'excelField' => $fields,
                'count' => $count
            ];
            
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }
}