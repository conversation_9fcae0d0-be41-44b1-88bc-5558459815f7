<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use think\Db;
use think\Exception;

class Salary extends Backend
{
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Engineer;
    }

    /**
     * 工资结算统计
     */
    public function index()
    {
        $this->request->filter(['strip_tags', 'trim']);
        
        // 获取所有工程师
        $engineers = Db::name('engineer')->field('id, name, mobile, area, tags')->select();
        $this->view->assign('engineers', $engineers);
        
        // 获取时间范围
        $startDate = $this->request->get('start_date', date('Y-m-01'));
        $endDate = $this->request->get('end_date', date('Y-m-d'));
        
        $this->view->assign('startDate', $startDate);
        $this->view->assign('endDate', $endDate);
        
        if ($this->request->isAjax()) {
            $engineerId = $this->request->get('engineer_id', '');
            $status = $this->request->get('status', '已完工');
            
            try {
                // 构建查询
                $query = Db::name('order')
                    ->field([
                        'SUM(CASE WHEN `工单状态` = "已完工" THEN 1 ELSE 0 END) as completed_count',
                        'SUM(CASE WHEN `工单状态` = "已取消" THEN 1 ELSE 0 END) as canceled_count',
                        'SUM(CASE WHEN `工单状态` = "已完工" THEN `结算金额` ELSE 0 END) as total_amount',
                        'AVG(CASE WHEN `工单状态` = "已完工" THEN `结算金额` ELSE 0 END) as avg_amount',
                        '配送工程师 as engineer_name',
                        'COUNT(*) as total_orders'
                    ])
                    ->whereTime('订单接入时间', 'between', [$startDate, $endDate]);
                
                // 筛选特定工程师
                if (!empty($engineerId)) {
                    $engineer = Db::name('engineer')->where('id', $engineerId)->find();
                    if ($engineer) {
                        $query->where('配送工程师', $engineer['name']);
                    }
                }
                
                // 根据工程师分组
                $salaryData = $query->group('配送工程师')
                    ->having('配送工程师', '<>', '')
                    ->select();
                
                // 计算总计
                $totalStats = [
                    'total_orders' => 0,
                    'completed_count' => 0,
                    'canceled_count' => 0,
                    'total_amount' => 0,
                ];
                
                foreach ($salaryData as $item) {
                    $totalStats['total_orders'] += $item['total_orders'];
                    $totalStats['completed_count'] += $item['completed_count'];
                    $totalStats['canceled_count'] += $item['canceled_count'];
                    $totalStats['total_amount'] += $item['total_amount'];
                }
                
                $totalStats['avg_amount'] = $totalStats['completed_count'] > 0 ? 
                    $totalStats['total_amount'] / $totalStats['completed_count'] : 0;
                
                return json([
                    'total' => count($salaryData),
                    'rows' => $salaryData,
                    'summary' => $totalStats
                ]);
                
            } catch (Exception $e) {
                return json(['total' => 0, 'rows' => [], 'msg' => $e->getMessage()]);
            }
        }
        
        return $this->view->fetch();
    }
    
    /**
     * 工程师工资详情
     */
    public function detail($engineer = null)
    {
        if (!$engineer) {
            $this->error('参数错误');
        }
        
        // 获取时间范围
        $startDate = $this->request->get('start_date', date('Y-m-01'));
        $endDate = $this->request->get('end_date', date('Y-m-d'));
        
        // 获取工程师信息
        $engineerInfo = Db::name('engineer')->where('name', $engineer)->find();
        if (!$engineerInfo) {
            $this->error('工程师不存在');
        }
        
        // 获取该工程师的订单列表
        $orderList = Db::name('order')
            ->where('配送工程师', $engineer)
            ->whereTime('订单接入时间', 'between', [$startDate, $endDate])
            ->order('id desc')
            ->select();
        
        // 计算统计数据
        $stats = [
            'total_orders' => count($orderList),
            'completed_count' => 0,
            'canceled_count' => 0,
            'total_amount' => 0,
            'avg_amount' => 0
        ];
        
        foreach ($orderList as $order) {
            if ($order['工单状态'] == '已完工') {
                $stats['completed_count']++;
                $stats['total_amount'] += floatval($order['结算金额']);
            } elseif ($order['工单状态'] == '已取消') {
                $stats['canceled_count']++;
            }
        }
        
        $stats['avg_amount'] = $stats['completed_count'] > 0 ? 
            $stats['total_amount'] / $stats['completed_count'] : 0;
        
        $this->view->assign('engineer', $engineer);
        $this->view->assign('engineerInfo', $engineerInfo);
        $this->view->assign('orderList', $orderList);
        $this->view->assign('stats', $stats);
        $this->view->assign('startDate', $startDate);
        $this->view->assign('endDate', $endDate);
        
        return $this->view->fetch();
    }
    
    /**
     * 导出工资统计
     */
    public function export()
    {
        $startDate = $this->request->get('start_date', date('Y-m-01'));
        $endDate = $this->request->get('end_date', date('Y-m-d'));
        $engineerId = $this->request->get('engineer_id', '');
        
        try {
            // 构建查询
            $query = Db::name('order')
                ->field([
                    'SUM(CASE WHEN `工单状态` = "已完工" THEN 1 ELSE 0 END) as completed_count',
                    'SUM(CASE WHEN `工单状态` = "已取消" THEN 1 ELSE 0 END) as canceled_count',
                    'SUM(CASE WHEN `工单状态` = "已完工" THEN `结算金额` ELSE 0 END) as total_amount',
                    'AVG(CASE WHEN `工单状态` = "已完工" THEN `结算金额` ELSE 0 END) as avg_amount',
                    '配送工程师 as engineer_name',
                    'COUNT(*) as total_orders'
                ])
                ->whereTime('订单接入时间', 'between', [$startDate, $endDate]);
            
            // 筛选特定工程师
            if (!empty($engineerId)) {
                $engineer = Db::name('engineer')->where('id', $engineerId)->find();
                if ($engineer) {
                    $query->where('配送工程师', $engineer['name']);
                }
            }
            
            // 根据工程师分组
            $salaryData = $query->group('配送工程师')
                ->having('配送工程师', '<>', '')
                ->select();
            
            // 导出Excel
            $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
            $worksheet = $spreadsheet->getActiveSheet();
            
            // 设置标题
            $worksheet->setCellValue('A1', '工程师姓名');
            $worksheet->setCellValue('B1', '总工单数');
            $worksheet->setCellValue('C1', '已完工数量');
            $worksheet->setCellValue('D1', '已取消数量');
            $worksheet->setCellValue('E1', '结算总金额');
            $worksheet->setCellValue('F1', '平均结算金额');
            
            // 填充数据
            $row = 2;
            foreach ($salaryData as $item) {
                $worksheet->setCellValue('A' . $row, $item['engineer_name']);
                $worksheet->setCellValue('B' . $row, $item['total_orders']);
                $worksheet->setCellValue('C' . $row, $item['completed_count']);
                $worksheet->setCellValue('D' . $row, $item['canceled_count']);
                $worksheet->setCellValue('E' . $row, $item['total_amount']);
                $worksheet->setCellValue('F' . $row, $item['avg_amount']);
                $row++;
            }
            
            // 下载文件
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="工资结算统计_' . date('YmdHis') . '.xlsx"');
            header('Cache-Control: max-age=0');
            
            $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
            $writer->save('php://output');
            exit;
            
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
    }
}