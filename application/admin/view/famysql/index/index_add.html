<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" />
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Column_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-column_name" data-rule="required" data-source="famysql/index/selectpage" data-params='{"custom[table]":"{$table|htmlentities}"}' data-primary-key="column_name" data-field="column_name" data-pagination="true" data-page-size="10" data-multiple="true" class="form-control selectpage" name="row[column_name]" type="text" />			
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Non_unique')}:</label>
        <div class="col-xs-12 col-sm-8">
			<!--给select一个固定的高度-->
			<select id="field-type" class="form-control form-selection" name="row[non_unique]" style="height:31px;">
				<option value="">----请选择----</option>
				{foreach name="indexList" item="vo"}
                <option value="{$key}" {in name="key" value=""}selected{/in}>{$vo}</option>
                {/foreach}
			</select>
        </div>
    </div>    
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
