<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
	{if $group == null}
	<div class="form-group">
		<label class="control-label col-xs-12 col-sm-2">{:__('Addon')}：</label>
		<div class="col-xs-12 col-sm-8">
			<select id="c-addon" data-rule="required" class="form-control selectpicker" name="row[addon]">
				{foreach name="groupsList" item="vo"}
				<option value="{$key}" {in name="key" value="" }selected{/in}>{$vo}</option>
				{/foreach}
			</select>
		</div>
	</div>
	{else}
	<input id="c-addon" data-rule="required" class="form-control form-control" placeholder="{:__('Addon')}" name="row[addon]"
				type="hidden" value="{$group}">
    {/if}
	
	<div class="form-group">
		<label class="control-label col-xs-12 col-sm-2">{:__('Name')}：</label>
		<div class="col-xs-12 col-sm-8">
			<input id="c-name" data-rule="required" data-source="famysql/table/selectnames" data-format-item="#titletpl" data-primary-key="table_name" data-field="table_name" data-multiple="true" data-pagination="true" data-page-size="10" class="form-control selectpage" name="row[name]" type="text" />
		</div>
	</div>

	<div class="form-group layer-footer">
		<label class="control-label col-xs-12 col-sm-2"></label>
		<div class="col-xs-12 col-sm-8">
			<button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
			<button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
		</div>
	</div>
</form>
<script type="text/html" id="titletpl">
    <%=table_name%> - <%=comment%>
</script>