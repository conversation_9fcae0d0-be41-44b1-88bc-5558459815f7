<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    {if $group == null}
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('插件')}：</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-addon" data-rule="required" class="form-control selectpicker" name="row[addon]">
                {foreach name="groups" item="vo"}
                <option value="{$key}" {in name="key" value="" }selected{/in}>{$vo}</option>
                {/foreach}
            </select>
        </div>
    </div>
    {else}
	<input id="c-addon" data-rule="required" class="form-control form-control" placeholder="插件" name="row[addon]"
				type="hidden" value="{$group|htmlentities}">
    {/if}
    
    <div class="form-group">
        <label for="c-type" class="control-label col-xs-12 col-sm-2">{:__('类型')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[type]', ['0'=>__('结构唯一'), '1'=>__('仅有数据'), '2'=>__('结构和数据')])}
        </div>
    </div>

    <div class="form-group">
        <label for="c-ignore_tables" class="control-label col-xs-12 col-sm-2">{:__('忽略的表')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" id="c-ignore_tables" name="row[ignore_tables]" class="form-control selectpage" data-source="famysql/table/get_table_list" data-field="table_name" data-primary-key="table_name" data-pagination="true" data-multiple="true" placeholder="请选择忽略的表" />
        </div>
    </div>

    <div class="form-group layer-footer">
		<label class="control-label col-xs-12 col-sm-2"></label>
		<div class="col-xs-12 col-sm-8">
			<button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
			<button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
		</div>
	</div>
</form>