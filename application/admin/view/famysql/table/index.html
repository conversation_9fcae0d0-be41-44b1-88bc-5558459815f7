<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        {:build_heading(null,FALSE)}
        {if $group == null}
        <ul class="nav nav-tabs" data-field="group">
            <li class="active"><a href="#system" data-value="system" data-toggle="tab">{:__('系统')}</a></li>
            {foreach name="groups" item="vo"}
            <li><a href="#{$key}" data-value="{$key}" data-toggle="tab">{$vo}</a></li>
            {/foreach}
        </ul>
        {/if}

    </div>
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        {:build_toolbar('refresh,add')}
                        {if $auth->check('famysql/table/table_batch_add')}
                        <a class="btn btn-warning btn-dialog" href="{:url('famysql.table/table_batch_add',['group'=>$group])}" title="{:__('Batch_add')}"><i class="fa fa-list-alt"></i> {:__('Batch_add')}</a>
                        {/if}
                        {if $auth->check('famysql/table/backuplist')}
                        <a class="btn btn-info btn-dialog" href="{:url('famysql.table/backuplist',['group'=>$group])}" data-area='["80%","80%"]' title="{:__('Backup and Restore')}"><i class="fa fa-compress"></i> {:__('Backup and Restore')}</a>
                        {/if}                        
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover"
                        data-operate-edit="{:$auth->check('famysql/table/table_edit')}"
                        data-operate-del="{:$auth->check('famysql/table/table_del')}" width="100%">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
