<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

	<div class="form-group">
		<label class="control-label col-xs-12 col-sm-2">{:__('Name')}：</label>
		<div class="col-xs-12 col-sm-8">
			<input id="c-name" data-rule="required" class="form-control" placeholder="{:__('Name placeholder')}" name="row[name]" type="text" value="{$row.name|htmlentities}">
		</div>
	</div>

	<div class="form-group">
		<label class="control-label col-xs-12 col-sm-2">{:__('Engine')}：</label>
		<div class="col-xs-12 col-sm-8">
			{:build_radios('row[engine]', ['InnoDB'=>__('InnoDB'), 'MyISAM'=>__('MyISAM')], $row['engine'])}
		</div>
	</div>
	
	<div class="form-group">
		<label class="control-label col-xs-12 col-sm-2">{:__('Charset')}：</label>
		<div class="col-xs-12 col-sm-8">
			<select id="c-charset" data-rule="required" class="form-control selectpicker" name="row[charset]">
				{foreach name="charsetList" item="vo"}
				<option value="{$vo}" {in name="vo" value="$row.charset" }selected{/in}>{$vo}</option>
				{/foreach}
			</select>
		</div>
	</div>

	<div class="form-group">
		<label class="control-label col-xs-12 col-sm-2">{:__('Collation')}：</label>
		<div class="col-xs-12 col-sm-8">
			<input id="c-collation" data-rule="required" class="form-control selectpage"
				data-source="famysql/table/getCollation" data-field="collation" data-primary-key="collation" name="row[collation]" type="text" value="{$row.collation|htmlentities}">
		</div>
	</div>

	<div class="form-group">
		<label class="control-label col-xs-12 col-sm-2">{:__('Comment')}：</label>
		<div class="col-xs-12 col-sm-8">
			<input id="c-comment" data-rule="required" class="form-control" name="row[comment]" type="text" value="{$row.comment|htmlentities}">
		</div>
	</div>
	<div class="form-group layer-footer">
		<label class="control-label col-xs-12 col-sm-2"></label>
		<div class="col-xs-12 col-sm-8">
			<button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
			<button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
		</div>
	</div>
</form>



