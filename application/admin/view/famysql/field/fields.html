<div class="panel panel-default panel-intro">
	<input id="assign-data-name" type="hidden" value="{$name|htmlentities}">
	<input id="assign-data-is_admin" type="hidden" value="{$is_admin|htmlentities}">
	{:build_heading()}
	<div class="panel-body">
		<div id="myTabContent" class="tab-content">
			<div class="panel-heading"><div class="panel-lead text-red"><b>温馨提示：</b>删除字段后，需要重新生成CRUD，否则会提示找不到字段！</div></div>
			<div class="tab-pane fade active in" id="one">
				<div class="widget-body no-padding">
					<div id="toolbar" class="toolbar">
						{if $is_admin}
						{:build_toolbar('refresh,add')}
						{if $auth->check('famysql/field/create')}
                        <a class="btn btn-warning btn-dialog" href="{:url('famysql.field/create',['name'=>$name,'is_admin'=>$is_admin])}" title="{:__('Batch generate')}"><i class="fa fa-plus"></i> {:__('Batch generate')}</a>
                        {/if}
						{else}
						{:build_toolbar('refresh')}
						{/if}												
					</div>
					<table id="table" class="table table-striped table-bordered table-hover"
						   data-operate-field_edit="{:$auth->check('famysql/field/field_edit')}"
						   data-operate-field_del="{:$auth->check('famysql/field/field_del')}"
						   width="100%">
					</table>
				</div>
			</div>
		</div>
	</div>
</div>