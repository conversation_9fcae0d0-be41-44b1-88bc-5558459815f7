<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-column_name" data-rule="required" data-source="famysql/field/selectfields" data-format-item="#titletpl" data-params='{"custom[table]":"{$name|htmlentities}"}' data-primary-key="column_name" data-field="column_name" data-multiple="true" data-pagination="true" data-page-size="10" class="form-control selectpage" name="row[column_name]" type="text" />			
        </div>
    </div>    
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
<script type="text/html" id="titletpl">
    <%=column_name%> - <%=comment%>
</script>
