<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
	<div class="panel-heading"><div class="panel-lead text-red"><b>温馨提示：</b>更新字段名称后，需要重新生成CRUD，不然更新的字段不会生效！</div></div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}：</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" placeholder="{:__('Name placeholder')}" name="row[name]" type="text" value="{$row.name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Type')}：</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-type" data-rule="required" class="form-control selectpage"
				data-source="famysql/field/getType" data-field="type" data-primary-key="type" name="row[type]" type="text" value="{$row.type|htmlentities}">
        </div>
    </div>   

	<div class="form-group form-input-basic{if !$is_enum} hidden{/if}">
		<label class="control-label col-xs-12 col-sm-2">{:__('Length')}：</label>
		<div class="col-xs-12 col-sm-8">
			<table class="table fieldlist" data-template="basictpl" data-name="row[length]">
				<tr>
					<td>{:__('Set vlaue')}</td>
				</tr>
				<tr>
					<td colspan="1"><a href="javascript:;" class="btn btn-sm btn-success btn-append"><i class="fa fa-plus"></i> {:__('Append')}</a></td>
				</tr>
			</table>

			<!--请注意实际开发中textarea应该添加个hidden进行隐藏-->
			<textarea name="row[length]" {if !$is_enum}disabled{/if} class="form-control hidden" cols="30" rows="5">{if $is_enum}{$row.length|htmlentities}{/if}</textarea>
			<script id="basictpl" type="text/html">
				<tr class="form-inline">
					<td><input type="text" name="<%=name%>[<%=index%>][vo]" class="form-control" size="15" value="<%=row.vo%>" placeholder="{:__('Set vlaue')}"/></td>
					<td>
						<!--下面的两个按钮务必保留-->
						<span class="btn btn-sm btn-danger btn-remove"><i class="fa fa-times"></i></span>
						<span class="btn btn-sm btn-primary btn-dragsort"><i class="fa fa-arrows"></i></span>
					</td>
				</tr>
			</script>
		</div>
	</div>

	<div class="form-group form-input-length{if $is_enum || $is_length} hidden{/if}">
		<label class="control-label col-xs-12 col-sm-2">{:__('Length')}：</label>
		<div class="col-xs-12 col-sm-8">
			<input id="c-length" class="form-control"{if $is_enum || $is_length} disabled{/if} placeholder="{:__('Length placeholder')}" name="row[length]" type="text" value="{$row.length|htmlentities}" />
		</div>
	</div>

	<div class="form-group form-input-default{if $is_length} hidden{/if}">
		<label class="control-label col-xs-12 col-sm-2">{:__('Default')}：</label>
		<div class="col-xs-12 col-sm-8">
			<input id="c-default" data-rule=""{if $is_length} disabled{/if} class="form-control" placeholder="{:__('Default placeholder')}" name="row[default]" type="text" value="{$row.default|htmlentities}" />
		</div>
	</div>

	<div class="form-group form-input-is_null">
		<label class="control-label col-xs-12 col-sm-2">{:__('Is_null')}：</label>
		<div class="col-xs-12 col-sm-8">
			{:build_radios('row[is_null]', ['1'=>'否','0'=>'是'], $row['is_null'] == 'NO' ? 0 : 1)}
		</div>
	</div>

	<div class="form-group form-input-unsigned{if !$is_int} hidden{/if}">
		<label class="control-label col-xs-12 col-sm-2">{:__('Unsigned')}：</label>
		<div class="col-xs-12 col-sm-8">
			{:build_radios('row[unsigned]', ['0'=>'否','1'=>'是'], $row['unsigned'])}
		</div>
	</div>

	<div class="form-group form-input-zerofill{if !$is_int} hidden{/if}">
		<label class="control-label col-xs-12 col-sm-2">{:__('Zerofill')}：</label>
		<div class="col-xs-12 col-sm-8">
			{:build_radios('row[zerofill]', ['0'=>'否','1'=>'是'], $row['zerofill'])}
		</div>
	</div>

	<div class="form-group">
		<label class="control-label col-xs-12 col-sm-2">{:__('Comment')}：</label>
		<div class="col-xs-12 col-sm-8">
			<input id="c-comment"  class="form-control" placeholder="{:__('Comment placeholder')}" name="row[comment]" type="text" value="{$row.comment|htmlentities}" />
		</div>
	</div>    
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
