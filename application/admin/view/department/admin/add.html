<form id="add-form" class="form-horizontal form-ajax" role="form" data-toggle="validator" method="POST" action="">
    {:token()}
    <div class="form-group">
        <label for="username" class="control-label col-xs-12 col-sm-2">{:__('Username')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control" id="username" name="row[username]" value="" data-rule="required;username" />
        </div>
    </div>
    <div class="form-group">
        <label for="nickname" class="control-label col-xs-12 col-sm-2">{:__('姓名')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control" id="nickname" name="row[nickname]" autocomplete="off" value="" data-rule="required" />
        </div>
    </div>
    <div class="form-group">
        <label for="password" class="control-label col-xs-12 col-sm-2">{:__('Password')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control" id="password" name="row[password]" autocomplete="new-password" value="{:\\fast\\Random::numeric(8)}" data-rule="required;password" />
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Department')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_select('department_id[]', $departmentdata, null, ['class'=>'form-control selectpicker','id'=>'department_ids', 'multiple'=>'false', 'data-rule'=>'required'])}
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Group')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_select('group[]', $groupdata, null, ['class'=>'form-control selectpicker', 'multiple'=>'false', 'data-rule'=>'required'])}
        </div>
    </div>



    {if $exits_mobile}
    <div class="form-group">
        <label for="mobile" class="control-label col-xs-12 col-sm-2">{:__('手机')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="mobile" class="form-control" id="mobile" name="row[mobile]" value="" />
        </div>
    </div>
    {/if}

    <div class="form-group">
        <label for="email" class="control-label col-xs-12 col-sm-2">{:__('Email')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="email" class="form-control" id="email" name="row[email]" value="" data-rule="required;email" />
        </div>
    </div>

    {if $auth->isSuperAdmin()}
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('数据范围')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[data_scope]', ['1'=>__('全部'), '0'=>__('默认')], 0)}
        </div>
    </div>
    {/if}

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('部门负责人')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('is_principal', ['1'=>__('是'), '0'=>__('否')], 0)}
        </div>
    </div>



    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('入职状态')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[status]', ['normal'=>__('Normal'), 'hidden'=>__('离职')])}
        </div>
    </div>
    <div class="form-group hidden layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>