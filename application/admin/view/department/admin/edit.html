<form id="edit-form" class="form-horizontal form-ajax" role="form" data-toggle="validator" method="POST" action="">
    {:token()}
    <div class="form-group">
        <label for="username" class="control-label col-xs-12 col-sm-2">{:__('Username')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control" id="username" name="row[username]" value="{$row.username|htmlentities}" data-rule="required;username" />
        </div>
    </div>
    <div class="form-group">
        <label for="nickname" class="control-label col-xs-12 col-sm-2">{:__('姓名')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control" id="nickname" name="row[nickname]" autocomplete="off" value="{$row.nickname|htmlentities}" data-rule="required" />
        </div>
    </div>
    <div class="form-group">
        <label for="password" class="control-label col-xs-12 col-sm-2">{:__('Password')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="password" class="form-control" id="password" name="row[password]" autocomplete="new-password" value="" data-rule="password" />

        </div>
        <div class="help-block">{:__('留空不修改密码')}</div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Department')}:</label>
        <div class="col-xs-12 col-sm-8">

            {:build_select('department_id[]', $departmentdata, $department_ids, ['class'=>'form-control selectpicker','id'=>'department_ids', 'multiple'=>'false', 'data-rule'=>'required'])}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Group')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_select('group[]', $groupdata, $groupids, ['class'=>'form-control selectpicker', 'multiple'=>'false', 'data-rule'=>'required'])}
        </div>
    </div>




    {if $exits_mobile}
    <div class="form-group">
        <label for="mobile" class="control-label col-xs-12 col-sm-2">{:__('手机')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="mobile" class="form-control" id="mobile" name="row[mobile]" value="{$row.mobile|htmlentities}"  />
        </div>
    </div>
    {/if}
    <div class="form-group">
        <label for="email" class="control-label col-xs-12 col-sm-2">{:__('Email')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="email" class="form-control" id="email" name="row[email]" value="{$row.email|htmlentities}" data-rule="required;email" />
        </div>
    </div>

    <div class="form-group">
        <label for="loginfailure" class="control-label col-xs-12 col-sm-2">{:__('Loginfailure')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="number" class="form-control" id="loginfailure" name="row[loginfailure]" value="{$row.loginfailure}" data-rule="required" />
        </div>
    </div>
    {if $auth->isSuperAdmin()}
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('数据范围')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[data_scope]', ['1'=>__('全部'), '0'=>__('默认')], $row['data_scope'])}
        </div>
    </div>
    {/if}
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('状态')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[status]', ['normal'=>__('Normal'), 'hidden'=>__('离职')], $row['status'])}
            <div class="help-block text-red">{:__('离职帐号将不能登录系统')}</div>
        </div>

    </div>


    <div class="form-group hidden layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>