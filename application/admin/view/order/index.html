<div class="panel panel-default panel-intro">
    {:build_heading()}

    <!-- 添加二维码相关CSS -->
    <style>
    /* 二维码容器样式 */
    .qrcode-container {
        display: inline-flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s;
        padding: 4px;
        border-radius: 4px;
        background: #fff;
        border: 1px solid #eee;
        max-width: 60px;
        position: relative;
        min-height: 60px;
    }

    .qrcode-container:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }

    /* 二维码图片样式 */
    .qrcode-img {
        width: 40px;
        height: 40px;
        object-fit: contain;
        display: none; /* 初始隐藏 */
    }

    /* 加载动画 */
    .qrcode-loading {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .qrcode-loading-spinner {
        width: 20px;
        height: 20px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #3498db;
        border-radius: 50%;
        animation: qrcode-spin 0.8s linear infinite;
    }

    @keyframes qrcode-spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* 二维码下方文字 */
    .qrcode-text {
        font-size: 10px;
        margin-top: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
    }

    /* 错误状态 */
    .qrcode-error {
        width: 40px;
        height: 40px;
        display: none; /* 初始隐藏 */
        align-items: center;
        justify-content: center;
        background: #fff8f8;
        color: #ff4d4f;
        font-size: 10px;
        border: 1px dashed #ffccc7;
    }

    /* 大二维码样式 */
    .qrcode-large {
        width: 200px;
        height: 200px;
        object-fit: contain;
        margin: 0 auto;
        display: block;
    }

    /* 运单号文本 */
    .tracking-number {
        font-family: monospace;
        font-size: 14px;
        margin-top: 10px;
        padding: 5px;
        background: #f5f5f5;
        border-radius: 4px;
        word-break: break-all;
    }

    /* 模态框样式 */
    .qrcode-modal .modal-dialog {
        max-width: 280px;
    }
     /* 工程师分配相关样式 */
     .engineer-selector {
        margin-bottom: 15px;
    }
    
    .engineer-list {
        max-height: 300px;
        overflow-y: auto;
    }
    
    .engineer-item {
        padding: 10px;
        border-bottom: 1px solid #eee;
        cursor: pointer;
        transition: background 0.2s;
    }
    
    .engineer-item:hover {
        background-color: #f5f5f5;
    }
    
    .engineer-item .tag {
        background: #eee;
        padding: 2px 5px;
        border-radius: 3px;
        font-size: 12px;
        margin-right: 5px;
    }


.engineer-list-container {
    max-height: 400px;
    overflow-y: auto;
}

.engineer-item {
    cursor: pointer;
    transition: background-color 0.2s;
}

.engineer-item:hover {
    background-color: #f8f9fa;
}

.modal-backdrop {
    opacity: 0.3;
}

.modern-modal .modal-dialog {
    width: 500px;
    margin: 30px auto;
}
    </style>

<div class="panel-body">
    <div id="myTabContent" class="tab-content">
        <div class="tab-pane fade active in" id="one">
            <div class="widget-body no-padding">
                <div id="toolbar" class="toolbar">
                    <!-- 工具栏按钮 -->
                    <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}"><i class="fa fa-refresh"></i></a>
                    <a href="javascript:;" class="btn btn-success btn-add {:$auth->check('order/add')?'':'hide'}" title="{:__('Add')}"><i class="fa fa-plus"></i> {:__('Add')}</a>
                    <a href="javascript:;" class="btn btn-success btn-edit btn-disabled disabled {:$auth->check('order/edit')?'':'hide'}" title="{:__('Edit')}"><i class="fa fa-pencil"></i> {:__('Edit')}</a>
                    <a href="javascript:;" class="btn btn-danger btn-del btn-disabled disabled {:$auth->check('order/del')?'':'hide'}" title="{:__('Delete')}"><i class="fa fa-trash"></i> {:__('Delete')}</a>
                    <a href="javascript:;" data-toggle="importguide" class="btn btn-success {:$auth->check('import/log/add')?'':'hide'}" title="{:__('导入向导')}"><i class="fa fa-eye"></i> {:__('导入')}</a>
                    <!-- 添加批量分配工程师按钮 -->
                    <a href="javascript:;" class="btn btn-info btn-assign-engineer btn-disabled disabled {:$auth->check('order/assignEngineer')?'':'hide'}" title="{:__('分配工程师')}"><i class="fa fa-user-plus"></i> {:__('分配工程师')}</a>
                    <!-- 添加批量修改工单状态按钮 -->
                    <a href="javascript:;" class="btn btn-warning btn-batch-status btn-disabled disabled {:$auth->check('order/updateWorkStatus')?'':'hide'}" title="{:__('批量修改状态')}"><i class="fa fa-tasks"></i> {:__('批量修改状态')}</a>
                </div>
                <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                       data-operate-edit="{:$auth->check('order/edit')}"
                       data-operate-del="{:$auth->check('order/del')}"
                       width="100%">
                </table>
            </div>
        </div>
    </div>
</div>

    <!-- 二维码模态框模板 -->
    <div class="modal fade qrcode-modal" id="qrcodeModalTemplate" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">运单号二维码</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body text-center">
                    <div class="qrcode-loading">
                        <div class="qrcode-loading-spinner"></div>
                    </div>
                    <img class="qrcode-large" style="display: none;">
                    <div class="qrcode-error" style="display: none;">生成失败</div>
                    <p class="tracking-number mt-2"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary btn-copy">复制</button>
                </div>

                <!-- 工程师选择模态框 -->
    <div class="modal fade" id="assignEngineerModal" tabindex="-1" role="dialog" aria-labelledby="engineerModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title" id="engineerModalLabel">选择配送工程师</h4>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <input type="text" class="form-control" id="engineerSearch" placeholder="搜索工程师名称、电话或标签">
                    </div>
                    <div class="engineer-list">
                        {volist name="engineers" id="engineer"}
                        <div class="engineer-item" data-id="{$engineer.id}" data-name="{$engineer.name}">
                            <div class="row">
                                <div class="col-xs-7">
                                    <strong>{$engineer.name}</strong> 
                                    <small class="text-muted">{$engineer.mobile}</small>
                                </div>
                                <div class="col-xs-5 text-right">
                                    <span class="text-primary">{$engineer.area}</span>
                                    <div>
                                        {volist name="engineer.tags|explode=','" id="tag"}
                                        <span class="tag">{$tag}</span>
                                        {/volist}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {/volist}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                </div>
            </div>
        </div>
    </div>
</div>

            </div>
        </div>
    </div>
</div>