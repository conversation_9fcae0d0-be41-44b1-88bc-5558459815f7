<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工单详情 - 物流管理系统</title>
    <!-- 使用国内BootCDN资源 -->
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap-icons/1.10.0/font/bootstrap-icons.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2c3e50;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-bg: #f8f9fa;
            --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            --card-border-radius: 10px;
            --section-spacing: 24px;
        }
        
        body {
            font-family: 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1600px;
            padding: 30px;
        }
        
        .order-header {
            background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
            color: white;
            border-radius: var(--card-border-radius) var(--card-border-radius) 0 0;
            padding: 24px 30px;
            margin-bottom: 0;
        }
        
        .order-card {
            border: none;
            border-radius: var(--card-border-radius);
            box-shadow: var(--card-shadow);
            margin-bottom: var(--section-spacing);
            overflow: hidden;
            background-color: white;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .order-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
        }
        
        .card-header {
            background-color: white;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            padding: 18px 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            font-size: 16px;
        }
        
        .card-header i {
            margin-right: 10px;
            color: var(--primary-color);
            font-size: 1.1em;
        }
        
        .card-body {
            padding: 24px;
        }
        
        .info-item {
            display: flex;
            margin-bottom: 14px;
            padding-bottom: 14px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .info-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        
        .info-label {
            width: 160px;
            font-weight: 500;
            color: #666;
            flex-shrink: 0;
            font-size: 14px;
        }
        
        .info-value {
            flex-grow: 1;
            color: #333;
            word-break: break-word;
            font-size: 14px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            letter-spacing: 0.5px;
        }
        
        .badge-primary {
            background-color: rgba(52, 152, 219, 0.1);
            color: var(--primary-color);
            border: 1px solid rgba(52, 152, 219, 0.2);
        }
        
        .badge-success {
            background-color: rgba(46, 204, 113, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(46, 204, 113, 0.2);
        }
        
        .badge-warning {
            background-color: rgba(243, 156, 18, 0.1);
            color: var(--warning-color);
            border: 1px solid rgba(243, 156, 18, 0.2);
        }
        
        .badge-danger {
            background-color: rgba(231, 76, 60, 0.1);
            color: var(--danger-color);
            border: 1px solid rgba(231, 76, 60, 0.2);
        }
        
        .qr-container {
            width: 160px;
            height: 160px;
            background-color: #f8f9fa;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            margin: 0 auto 16px;
            border: 1px solid #eee;
        }
        
        /* 空状态样式 */
        .empty-state {
            padding: 40px 20px;
            text-align: center;
            color: #999;
            background-color: #fafafa;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        .empty-state i {
            font-size: 40px;
            margin-bottom: 15px;
            opacity: 0.6;
        }
        
        /* 响应式调整 */
        @media (max-width: 992px) {
            .container {
                padding: 20px;
            }
            
            .card-header {
                padding: 16px 20px;
            }
            
            .card-body {
                padding: 20px;
            }
        }
        
        @media (max-width: 768px) {
            .info-item {
                flex-direction: column;
            }
            
            .info-label {
                width: 100%;
                margin-bottom: 6px;
            }
            
            .order-header {
                padding: 20px;
            }
            
            .status-badge {
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <div class="order-card">
            <div class="order-header">
                <div class="d-flex justify-content-between align-items-center flex-wrap">
                    <div>
                        <h2 class="mb-1">工单详情</h2>
                        <div class="d-flex align-items-center flex-wrap">
                            <span class="me-3">订单号: {$row['订单号']|default='--'}</span>
                            {if $row['标签']}
                            <span class="status-badge badge-primary me-2">{$row['标签']}</span>
                            {/if}
                            {if $row['是否需送装一体'] == 1}
                            <span class="status-badge badge-success me-2">送装一体</span>
                            {/if}
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card-body">
                <div class="row">
                    <!-- 左侧信息列 -->
                    <div class="col-lg-8">
                        <!-- 基本信息卡片 -->
                        <div class="order-card mb-4">
                            <div class="card-header">
                                <i class="bi bi-info-circle"></i> 基本信息
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="info-item">
                                            <div class="info-label">客户名称</div>
                                            <div class="info-value">{$row['客户名称']|default='--'}</div>
                                        </div>
                                        <div class="info-item">
                                            <div class="info-label">客户订单号</div>
                                            <div class="info-value">{$row['客户订单号']|default='--'}</div>
                                        </div>
                                        <div class="info-item">
                                            <div class="info-label">订单来源平台</div>
                                            <div class="info-value">{$row['订单来源平台']|default='--'}</div>
                                        </div>
                                        <div class="info-item">
                                            <div class="info-label">下单店铺</div>
                                            <div class="info-value">{$row['下单店铺']|default='--'}</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="info-item">
                                            <div class="info-label">商品名称</div>
                                            <div class="info-value">{$row['商品名称']|default='--'}</div>
                                        </div>
                                        <div class="info-item">
                                            <div class="info-label">品牌/品类</div>
                                            <div class="info-value">{$row['品牌']|default='--'} / {$row['品类']|default='--'}</div>
                                        </div>
                                        <div class="info-item">
                                            <div class="info-label">二级品牌</div>
                                            <div class="info-value">{$row['二级品牌']|default='--'}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 物流信息卡片 -->
                        <div class="order-card mb-4">
                            <div class="card-header">
                                <i class="bi bi-truck"></i> 物流信息
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="info-item">
                                            <div class="info-label">运单号</div>
                                            <div class="info-value">{$row['运单号']|default='--'}</div>
                                        </div>
                                        <!--<div class="info-item">-->
                                        <!--    <div class="info-label">物流单号</div>-->
                                        <!--    <div class="info-value">{$row['物流单号']|default='--'}</div>-->
                                        <!--</div>-->
                                        <!--<div class="info-item">-->
                                        <!--    <div class="info-label">快递单号</div>-->
                                        <!--    <div class="info-value">{$row['快递单号']|default='--'}</div>-->
                                        <!--</div>-->
                                    </div>
                                    <div class="col-md-6">
                                        <!--<div class="info-item">-->
                                        <!--    <div class="info-label">配送方式</div>-->
                                        <!--    <div class="info-value">{$row['配送方式']|default='--'}</div>-->
                                        <!--</div>-->
                                        <div class="info-item">
                                            <div class="info-label">是否需送装一体</div>
                                            <div class="info-value">
                                                {if $row['是否需送装一体'] == 1}
                                                <span class="status-badge badge-success">是</span>
                                                {else}
                                                <span class="text-muted">否</span>
                                                {/if}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 物流信息卡片后添加日志卡片 -->
                        <div class="order-card mb-4">
                            <div class="card-header">
                                <i class="bi bi-clock-history"></i> 操作日志
                            </div>
                            <div class="card-body">
                                {if !empty($logs)}
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>操作时间</th>
                                                <th>操作人</th>
                                                <th>操作类型</th>
                                                <th>操作内容</th>
                                                <th>操作IP</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {foreach $logs as $log}
                                            <tr>
                                                <td>{:date('Y-m-d H:i:s', $log['createtime'])}</td>
                                                <td>{$log['operator_name']}</td>
                                                <td>
                                                    <span class="status-badge badge-primary">{$log['action']}</span>
                                                </td>
                                                <td>{$log['content']}</td>
                                                <td>{$log['ip']}</td>
                                            </tr>
                                            {/foreach}
                                        </tbody>
                                    </table>
                                </div>
                                {else}
                                <div class="empty-state">
                                    <i class="bi bi-clock-history"></i>
                                    <div>暂无操作日志</div>
                                </div>
                                {/if}
                            </div>
                        </div>
                    </div>
                    
                    <!-- 右侧信息列 -->
                    <div class="col-lg-4">
                        <!-- 二维码卡片 -->
                        <div class="order-card mb-4">
                            <div class="card-header">
                                <i class="bi bi-qr-code"></i> 运单二维码
                            </div>
                            <div class="card-body text-center">
                                {if $row['运单号']}
                                <div class="qr-container mb-3">
                                    <img id="waybill-qr" src="https://api.qrserver.com/v1/create-qr-code/?size=150x150&data={$row['运单号']}" alt="运单二维码">
                                </div>
                                <div class="text-muted">运单号: {$row['运单号']}</div>
                                {else}
                                <div class="empty-state">
                                    <i class="bi bi-qr-code"></i>
                                    <div>暂无运单信息</div>
                                </div>
                                {/if}
                            </div>
                        </div>
                        
                        <!-- 收货人信息卡片 -->
                        <div class="order-card mb-4">
                            <div class="card-header">
                                <i class="bi bi-person"></i> 收货人信息
                            </div>
                            <div class="card-body">
                                <div class="info-item">
                                    <div class="info-label">收货人</div>
                                    <div class="info-value">{$row['收货人名称']|default='--'}</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">联系电话</div>
                                    <div class="info-value">
                                        {php}
                                        // 优先使用虚拟电话
                                        if (!empty($row['收货人虚拟电话'])) {
                                            echo $row['收货人虚拟电话'];
                                        }
                                        // 其次使用电话1
                                        else if (!empty($row['收货人电话1'])) {
                                            echo $row['收货人电话1'];
                                        }
                                        // 最后使用电话2
                                        else if (!empty($row['收货人电话2'])) {
                                            echo $row['收货人电话2'];
                                        }
                                        // 都没有则返回默认值
                                        else {
                                            echo '--';
                                        }
                                        {/php}
                                    </div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">地址</div>
                                    <div class="info-value">
                                        {$row['收货人省名称']|default=''}{$row['收货人城市名称']|default=''}{$row['收货人区域名称']|default=''}{$row['收货人乡镇名称']|default=''}{$row['收货人详细地址']|default='--'}
                                    </div>
                                </div>
                                {if $row['客户捎话']}
                                <div class="info-item">
                                    <div class="info-label">客户捎话</div>
                                    <div class="info-value">{$row['客户捎话']}</div>
                                </div>
                                {/if}
                            </div>
                        </div>
                        
                        <!-- 工程师信息卡片 -->
                        <div class="order-card mb-4">
                            <div class="card-header">
                                <i class="bi bi-people"></i> 服务团队
                            </div>
                            <div class="card-body">
                                <div class="info-item">
                                    <div class="info-label">配送工程师</div>
                                    <div class="info-value">{$row['配送工程师']}</div>
                                </div>

                                {if !$row['配送工程师']}
                                <div class="empty-state">
                                    <i class="bi bi-person-x"></i>
                                    <div>暂无工程师信息</div>
                                </div>
                                {/if}
                            </div>
                        </div>
                        
                        <!-- 操作按钮卡片 -->
                        <!--<div class="order-card">-->
                        <!--    <div class="card-header">-->
                        <!--        <i class="bi bi-gear"></i> 操作-->
                        <!--    </div>-->
                        <!--    <div class="card-body action-buttons">-->
                        <!--        <button class="btn btn-outline-primary btn-sm">-->
                        <!--            <i class="bi bi-printer"></i> 打印工单-->
                        <!--        </button>-->
                        <!--        <button class="btn btn-outline-primary btn-sm">-->
                        <!--            <i class="bi bi-pencil"></i> 编辑信息-->
                        <!--        </button>-->
                        <!--        <button class="btn btn-outline-success btn-sm">-->
                        <!--            <i class="bi bi-check-circle"></i> 完成工单-->
                        <!--        </button>-->
                        <!--        <button class="btn btn-outline-warning btn-sm">-->
                        <!--            <i class="bi bi-calendar"></i> 改约时间-->
                        <!--        </button>-->
                        <!--        <button class="btn btn-outline-danger btn-sm">-->
                        <!--            <i class="bi bi-x-circle"></i> 取消工单-->
                        <!--        </button>-->
                        <!--        <button class="btn btn-outline-secondary btn-sm">-->
                        <!--            <i class="bi bi-telephone"></i> 联系客户-->
                        <!--        </button>-->
                        <!--        <button class="btn btn-outline-info btn-sm">-->
                        <!--            <i class="bi bi-chat-left-text"></i> 添加备注-->
                        <!--        </button>-->
                        <!--    </div>-->
                        <!--</div>-->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 使用国内BootCDN资源 -->
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        // 二维码加载失败处理
        document.getElementById('waybill-qr')?.addEventListener('error', function() {
            const qrContainer = document.querySelector('.qr-container');
            if (qrContainer) {
                qrContainer.innerHTML = '<div class="empty-state"><i class="bi bi-exclamation-triangle"></i><div>二维码生成失败</div></div>';
            }
        });
    </script>
</body>
</html>