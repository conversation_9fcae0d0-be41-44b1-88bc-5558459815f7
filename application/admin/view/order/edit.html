<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{:__('编辑工单')} #{$mappedRow.id}</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/3.4.1/css/bootstrap.min.css" rel="stylesheet">
    <link href="__CDN__/assets/css/backend.css?v={$Think.config.site.version}" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <div class="container-fluid">
        <div class="page-header">
            <h1><i class="fa fa-edit text-primary"></i> {:__('编辑工单')} <small class="text-muted">#{$mappedRow.id}</small></h1>
            <ol class="breadcrumb">
                <li><a href="javascript:;"><i class="fa fa-dashboard"></i> {:__('首页')}</a></li>
                <li><a href="javascript:;">{:__('工单')}</a></li>
                <li class="active">{:__('编辑工单')}</li>
            </ol>
        </div>
        
        <form id="edit-form" class="form-modern" role="form" data-toggle="validator" method="POST" action="">
            <input type="hidden" name="row[id]" value="{$mappedRow.id}">
            
            <div class="panel panel-default">
                <div class="panel-heading">
                    <i class="fa fa-file-text-o text-primary"></i> {:__('工单信息')}
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="c-order_number">{:__('订单号')}</label>
                                <input id="c-order_number" class="form-control" name="row[订单号]" type="text" value="{$mappedRow.order_number|htmlentities}" readonly>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="c-tag">{:__('标签')}</label>
                                <input id="c-tag" class="form-control" name="row[标签]" type="text" value="{$mappedRow.tag|htmlentities}">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="c-customer_order_number">{:__('客户订单号')}</label>
                                <input id="c-customer_order_number" class="form-control" name="row[客户订单号]" type="text" value="{$mappedRow.customer_order_number|htmlentities}" readonly>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="c-business_type">{:__('业务类型')}</label>
                                <input id="c-business_type" class="form-control" name="row[业务类型]" type="text" value="{$mappedRow.business_type|htmlentities}" readonly>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="c-waybill_number">{:__('运单号')}</label>
                                <input id="c-waybill_number" class="form-control" name="row[运单号]" type="text" value="{$mappedRow.waybill_number|htmlentities}" readonly>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="c-product_name">{:__('商品名称')}</label>
                                <input id="c-product_name" class="form-control" name="row[商品名称]" type="text" value="{$mappedRow.product_name|htmlentities}" readonly>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="c-work_order_number">{:__('工单号')}</label>
                                <input id="c-work_order_number" class="form-control" name="row[工单号]" type="text" value="{$mappedRow.work_order_number|htmlentities}" readonly>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="c-receiver_name">{:__('收货人名称')}</label>
                                <input id="c-receiver_name" class="form-control" name="row[收货人名称]" type="text" value="{$mappedRow.receiver_name|htmlentities}" readonly>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="c-receiver_phone1">{:__('收货人电话1')}</label>
                                <input id="c-receiver_phone1" class="form-control" name="row[收货人电话1]" type="text" value="{$mappedRow.receiver_phone1|htmlentities}" readonly>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="c-receiver_phone2">{:__('收货人电话2')}</label>
                                <input id="c-receiver_phone2" class="form-control" name="row[收货人电话2]" type="text" value="{$mappedRow.receiver_phone2|htmlentities}" readonly>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="c-receiver_virtual_phone">{:__('收货人虚拟电话')}</label>
                                <input id="c-receiver_virtual_phone" class="form-control" name="row[收货人虚拟电话]" type="text" value="{$mappedRow.receiver_virtual_phone|htmlentities}" readonly>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="c-receiver_address">{:__('收货人详细地址')}</label>
                                <input id="c-receiver_address" class="form-control" name="row[收货人详细地址]" type="text" value="{$mappedRow.receiver_address|htmlentities}">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="c-info_officer_remarks">{:__('信息员备注')}</label>
                                <input id="c-info_officer_remarks" class="form-control" name="row[信息员备注]" type="text" value="{$mappedRow.info_officer_remarks|htmlentities}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="c-engineer_remarks">{:__('工程师备注')}</label>
                                <input id="c-engineer_remarks" class="form-control" name="row[工程师备注]" type="text" value="{$mappedRow.engineer_remarks|htmlentities}">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="fixed-footer">
                <div class="pull-right">
                    <button type="submit" class="btn btn-primary btn-embossed btn-submit">
                        <i class="fa fa-save"></i> {:__('保存')}
                    </button>
                    <button type="reset" class="btn btn-default btn-embossed">
                        <i class="fa fa-refresh"></i> {:__('重置')}
                    </button>
                </div>
            </div>
        </form>
    </div>

<style>
/* 页面整体样式 */
body {
    background-color: #f5f7fa;
    padding-bottom: 60px; /* 为固定底部留出空间 */
}

.container-fluid {
    padding: 15px;
    max-width: 1400px;
    margin: 0 auto;
}

.page-header {
    padding-bottom: 15px;
    margin: 0 0 20px;
    border-bottom: 1px solid #eee;
}

.page-header h1 {
    font-size: 24px;
    margin-top: 0;
    margin-bottom: 10px;
    font-weight: 500;
}

.breadcrumb {
    background: none;
    padding: 0;
    margin-bottom: 0;
}

/* 面板样式优化 */
.panel {
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    margin-bottom: 15px;
    border: none;
}

.panel-heading {
    padding: 12px 15px;
    border-bottom: 1px solid #f0f0f0;
    background: #fbfbfb;
    font-size: 15px;
    font-weight: 500;
}

.panel-body {
    padding: 15px;
}

/* 表单控件美化 */
.form-group {
    margin-bottom: 12px;
}

.form-control {
    height: 32px;
    padding: 6px 10px;
    font-size: 13px;
    border-color: #dce0e6;
}

.form-control[readonly] {
    background-color: #f8f9fa;
    cursor: not-allowed;
}

label {
    font-size: 13px;
    margin-bottom: 4px;
}

/* 按钮样式 */
.btn {
    padding: 6px 12px;
    font-size: 13px;
}

.btn-primary {
    background-color: #3a86ff;
    border-color: #3a86ff;
}

.btn-primary:hover {
    background-color: #2c75e8;
    border-color: #2c75e8;
}

.btn-default {
    color: #6c757d;
    background-color: #fff;
    border-color: #dce0e6;
}

.btn-default:hover {
    background-color: #f8f9fa;
    border-color: #c0c6cc;
}

.btn i {
    margin-right: 5px;
}

/* 固定底部工具栏 */
.fixed-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    padding: 10px 15px;
    text-align: right;
    border-top: 1px solid #eee;
    z-index: 999;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .col-md-3 {
        margin-bottom: 10px;
    }
}
</style>

<script>
$(document).ready(function() {
    // 初始化表单验证
    Form.api.bindevent($("#edit-form"), function(data, ret) {
        // 成功提交回调
        Toastr.success(ret.msg || '保存成功');
        setTimeout(function () {
            window.location.href = document.referrer;
        }, 1500);
        return false;
    }, function(data, ret) {
        // 失败提交回调
        return false;
    });
});
</script>
</body>
</html>