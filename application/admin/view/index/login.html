<!DOCTYPE html>
<html lang="zh-CN">
<head>
    {include file="common/meta" /}
    
    <!-- 引入现代化字体和图标 -->
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style type="text/css">
        :root {
            --primary-500: #3B82F6;
            --primary-600: #2563EB;
            --primary-700: #1D4ED8;
            --gray-50: #F9FAFB;
            --gray-100: #F3F4F6;
            --gray-200: #E5E7EB;
            --gray-300: #D1D5DB;
            --gray-400: #9CA3AF;
            --gray-500: #6B7280;
            --gray-600: #4B5563;
            --gray-700: #374151;
            --gray-800: #1F2937;
            --gray-900: #111827;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Plus Jakarta Sans', -apple-system, BlinkMacSystemFont, sans-serif;
            color: var(--gray-800);
            background-color: var(--gray-50);
            min-height: 100vh;
            display: flex;
            line-height: 1.5;
        }
        
        /* 主容器 */
        .auth-container {
            display: flex;
            width: 100%;
            min-height: 100vh;
        }
        
        /* 左侧品牌区 - 高级玻璃渐变效果 */
        .auth-brand {
            flex: 1;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.8) 0%, rgba(29, 78, 216, 0.9) 100%);
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            position: relative;
            overflow: hidden;
            display: none;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }
        
        .auth-brand::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0) 70%);
            pointer-events: none;
            animation: gradientRotate 15s linear infinite;
        }
        
        @keyframes gradientRotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* CSS绘制的"逻界"Logo */
        .brand-logo {
            width: 120px;
            height: 120px;
            background-color: #111827;
            border-radius: 24px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 40px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            z-index: 2;
        }
        
        .brand-logo::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 45%, rgba(255,255,255,0.1) 50%, transparent 55%);
            background-size: 200% 200%;
            animation: shine 3s infinite;
        }
        
        @keyframes shine {
            0% { background-position: 100% 100%; }
            100% { background-position: 0% 0%; }
        }
        
        .logo-text {
            font-family: 'Plus Jakarta Sans', sans-serif;
            font-weight: 700;
            font-size: 36px;
            color: white;
            position: relative;
        }
        
        .brand-content {
            max-width: 480px;
            text-align: center;
            z-index: 1;
        }
        
        .brand-content h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 16px;
        }
        
        .brand-content p {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 32px;
        }
        
        /* 右侧表单区 */
        .auth-form-container {
            width: 100%;
            max-width: 480px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 40px;
            background-color: white;
            position: relative;
        }
        
        .auth-form-header {
            margin-bottom: 40px;
            text-align: center;
        }
        
        .auth-form-header h2 {
            font-size: 24px;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 8px;
        }
        
        .auth-form-header p {
            color: var(--gray-500);
            font-size: 14px;
        }
        
        /* 表单样式 */
        .auth-form {
            width: 100%;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 500;
            color: var(--gray-700);
        }
        
        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--gray-200);
            border-radius: 8px;
            font-size: 14px;
            transition: var(--transition);
            background-color: white;
            color: var(--gray-800);
        }
        
        .form-control:focus {
            outline: none;
            border-color: var(--primary-500);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
        }
        
        .input-with-icon {
            position: relative;
        }
        
        .input-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
            pointer-events: none;
        }
        
        .input-with-icon .form-control {
            padding-left: 42px;
        }
        
        /* 验证码样式 */
        .captcha-group {
            display: flex;
            gap: 12px;
        }
        
        .captcha-input {
            flex: 1;
        }
        
        .captcha-image {
            height: 44px;
            border-radius: 8px;
            border: 1px solid var(--gray-200);
            cursor: pointer;
            transition: var(--transition);
        }
        
        .captcha-image:hover {
            opacity: 0.9;
        }
        
        /* 记住我 */
        .remember-me {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
        }
        
        .remember-me input {
            margin-right: 8px;
            width: 16px;
            height: 16px;
            accent-color: var(--primary-500);
        }
        
        .remember-me label {
            font-size: 14px;
            color: var(--gray-600);
            cursor: pointer;
        }
        
        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            padding: 12px 16px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            transition: var(--transition);
            border: none;
        }
        
        .btn-primary {
            background-color: var(--primary-500);
            color: white;
            box-shadow: var(--shadow);
        }
        
        .btn-primary:hover {
            background-color: var(--primary-600);
            box-shadow: var(--shadow-md);
            transform: translateY(-1px);
        }
        
        .btn-primary:active {
            background-color: var(--primary-700);
            transform: translateY(0);
        }
        
        /* 错误提示 */
        .error-message {
            color: #EF4444;
            font-size: 13px;
            margin-top: 6px;
            display: none;
        }
        
        .has-error .form-control {
            border-color: #EF4444;
        }
        
        .has-error .error-message {
            display: block;
        }
        
        /* 页脚链接 */
        .auth-footer {
            margin-top: 32px;
            text-align: center;
            font-size: 13px;
            color: var(--gray-500);
        }
        
        /* 移动端适配 */
        @media (min-width: 768px) {
            .auth-brand {
                display: flex;
            }
            
            .auth-form-container {
                box-shadow: -4px 0 16px rgba(0, 0, 0, 0.05);
            }
            
            .mobile-logo {
                display: none;
            }
        }
        
        @media (max-width: 767px) {
            .auth-container {
                flex-direction: column;
            }
            
            .auth-form-container {
                max-width: 100%;
                padding: 32px 24px;
                margin: 0 auto;
            }
            
            .mobile-logo {
                display: flex;
                align-items: center;
                justify-content: center;
                margin-bottom: 24px;
            }
            
            .mobile-logo .brand-logo {
                width: 80px;
                height: 80px;
            }
            
            .mobile-logo .logo-text {
                font-size: 24px;
            }
        }
        
        /* 背景图条件判断 */
        {if $background}
        .auth-brand {
            background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('{$background|htmlentities}');
            background-size: cover;
            background-position: center;
        }
        {/if}
    </style>
</head>
<body>
    <div class="auth-container">
        <!-- 左侧品牌展示区 - 高级玻璃渐变效果 -->
        <div class="auth-brand">
            <div class="brand-logo">
                <div class="logo-text">逻界</div>
            </div>
            <div class="brand-content">
                <h1>企业级后台管理系统</h1>
                <p>高效、安全、可靠的后台管理解决方案</p>
                <div style="width: 80px; height: 4px; background: rgba(255,255,255,0.2); border-radius: 2px; margin: 0 auto 24px;"></div>
                <p>专业 · 高效 · 安全</p>
            </div>
        </div>
        
        <!-- 右侧登录表单区 -->
        <div class="auth-form-container">
            <!-- 移动端Logo -->
            <div class="mobile-logo">
                <div class="brand-logo">
                    <div class="logo-text">逻界</div>
                </div>
            </div>
            
            <div class="auth-form-header">
                <h2>欢迎回来</h2>
                <p>请输入您的凭证以访问管理系统</p>
            </div>
            
            <form action="" method="post" id="login-form" class="auth-form">
                <!--@AdminLoginFormBegin-->
                <div id="errtips" class="error-message hide"></div>
                {:token()}
                
                <div class="form-group">
                    <label for="pd-form-username" class="form-label">{:__('Username')}</label>
                    <div class="input-with-icon">
                        <i class="fas fa-user input-icon"></i>
                        <input type="text" class="form-control" id="pd-form-username" placeholder="请输入用户名" name="username" autocomplete="off" value="" data-rule="{:__('Username')}:required;username">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="pd-form-password" class="form-label">{:__('Password')}</label>
                    <div class="input-with-icon">
                        <i class="fas fa-lock input-icon"></i>
                        <input type="password" class="form-control" id="pd-form-password" placeholder="请输入密码" name="password" autocomplete="off" value="" data-rule="{:__('Password')}:required;password">
                    </div>
                </div>
                
                <!--@CaptchaBegin-->
                {if $Think.config.fastadmin.login_captcha}
                <div class="form-group">
                    <label class="form-label">{:__('Captcha')}</label>
                    <div class="captcha-group">
                        <div class="input-with-icon captcha-input">
                            <i class="fas fa-shield-alt input-icon"></i>
                            <input type="text" name="captcha" class="form-control" placeholder="验证码" data-rule="{:__('Captcha')}:required;length({$Think.config.captcha.length|htmlentities})" autocomplete="off">
                        </div>
                        <img src="{:rtrim('__PUBLIC__', '/')}/index.php?s=/captcha" class="captcha-image" onclick="this.src = '{:rtrim('__PUBLIC__', '/')}/index.php?s=/captcha&r=' + Math.random();">
                    </div>
                </div>
                {/if}
                <!--@CaptchaEnd-->
                
                {if $keeyloginhours>0}
                <div class="remember-me">
                    <input type="checkbox" name="keeplogin" id="keeplogin" value="1">
                    <label for="keeplogin">{:__('Keep login')} ({:__('The duration of the session is %s hours', $keeyloginhours)})</label>
                </div>
                {/if}
                
                <button type="submit" class="btn btn-primary">
                    {:__('Sign in')}
                    <i class="fas fa-arrow-right" style="margin-left: 8px;"></i>
                </button>
                <!--@AdminLoginFormEnd-->
            </form>
            
            <div class="auth-footer">
                <p>© {date('Y')} 逻界科技. All rights reserved.</p>
            </div>
        </div>
    </div>
    
    {include file="common/script" /}
    
    <script>
        // 增强交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 输入验证
            const form = document.getElementById('login-form');
            const inputs = form.querySelectorAll('.form-control');
            
            inputs.forEach(input => {
                // 输入框交互效果
                input.addEventListener('focus', function() {
                    this.parentElement.parentElement.classList.add('focused');
                });
                
                input.addEventListener('blur', function() {
                    this.parentElement.parentElement.classList.remove('focused');
                    validateInput(this);
                });
                
                // 实时验证
                input.addEventListener('input', function() {
                    validateInput(this);
                });
            });
            
            // 表单提交验证
            if(form) {
                form.addEventListener('submit', function(e) {
                    let isValid = true;
                    
                    inputs.forEach(input => {
                        if(!validateInput(input)) {
                            isValid = false;
                        }
                    });
                    
                    if(!isValid) {
                        e.preventDefault();
                        const errorTips = document.getElementById('errtips');
                        errorTips.classList.remove('hide');
                        errorTips.textContent = '请正确填写所有必填字段';
                        errorTips.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                });
            }
            
            // 输入验证函数
            function validateInput(input) {
                const formGroup = input.parentElement.parentElement;
                
                if(input.value.trim() === '') {
                    formGroup.classList.add('has-error');
                    const errorMsg = formGroup.querySelector('.error-message') || document.createElement('div');
                    if(!formGroup.querySelector('.error-message')) {
                        errorMsg.className = 'error-message';
                        formGroup.appendChild(errorMsg);
                    }
                    errorMsg.textContent = '此字段为必填项';
                    return false;
                } else {
                    formGroup.classList.remove('has-error');
                    const errorMsg = formGroup.querySelector('.error-message');
                    if(errorMsg) {
                        errorMsg.textContent = '';
                    }
                    return true;
                }
            }
            
            // 密码显示/隐藏切换
            const passwordInput = document.getElementById('pd-form-password');
            if(passwordInput) {
                const inputIcon = passwordInput.parentElement.querySelector('.input-icon');
                inputIcon.style.cursor = 'pointer';
                inputIcon.style.pointerEvents = 'auto';
                
                inputIcon.addEventListener('click', function() {
                    if(passwordInput.type === 'password') {
                        passwordInput.type = 'text';
                        inputIcon.classList.remove('fa-lock');
                        inputIcon.classList.add('fa-unlock');
                    } else {
                        passwordInput.type = 'password';
                        inputIcon.classList.remove('fa-unlock');
                        inputIcon.classList.add('fa-lock');
                    }
                });
            }
        });
    </script>
</body>
</html>