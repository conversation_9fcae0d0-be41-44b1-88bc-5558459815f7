<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('File_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-file_name" class="form-control" name="row[file_name]" type="text" value="{$row.file_name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Rwa_lang_json')}:</label>
        <div class="col-xs-12 col-sm-8">
            <dl class="fieldlist" data-name="row[raw_lang_json]" data-template="lang_tlp">
                <dd>
                    <ins>{:__('Key')}</ins>
                    <ins>{:__('Value')}</ins>
                </dd>
                <dd><a href="javascript:;" class="btn btn-sm btn-success btn-append"><i class="fa fa-plus"></i> {:__('Append')}</a></dd>
                <textarea name="row[raw_lang_json]" class="form-control hide" cols="30" rows="5">{$row.raw_lang_json}</textarea>
            </dl>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Lang_json')}:</label>
        <div class="col-xs-12 col-sm-8">

            <dl class="fieldlist" data-name="row[lang_json]" data-template="lang_tlp">
                <dd>
                    <ins>{:__('Key')}</ins>
                    <ins>{:__('Value')}</ins>
                </dd>
                <dd><a href="javascript:;" class="btn btn-sm btn-success btn-append"><i class="fa fa-plus"></i> {:__('Append')}</a></dd>
                <textarea name="row[lang_json]" class="form-control hide" cols="30" rows="5">{$row.lang_json}</textarea>
            </dl>
        </div>
    </div>

    <script type="text/html" id="lang_tlp">
        <dd class="form-inline">
            <input type="text" name="row[lang_json][<%=index%>][key]" class="form-control" value="<%=row.key%>" size="30">
            <input type="text" name="row[lang_json][<%=index%>][value]" class="form-control" value="<%=row.value%>" size="30">
            <span class="btn btn-sm btn-danger btn-remove"><i class="fa fa-times"></i></span>
        </dd>
    </script>

    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
<!--            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>-->
<!--            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>-->
        </div>
    </div>
</form>
