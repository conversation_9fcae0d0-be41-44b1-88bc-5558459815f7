<div class="panel panel-default panel-intro">
    {:build_heading()}

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('刷新')}"><i class="fa fa-refresh"></i></a>
                        <a href="javascript:;" class="btn btn-success btn-export" title="{:__('导出')}"><i class="fa fa-download"></i> {:__('导出结算数据')}</a>
                        
                        <!-- 日期筛选 -->
                        <div class="form-inline" style="display:inline-block;margin-left:10px;">
                            <div class="input-group">
                                <span class="input-group-addon">开始日期</span>
                                <input type="text" class="form-control js-datepicker" id="start_date" value="{$startDate}" data-date-format="yyyy-mm-dd">
                            </div>
                            <div class="input-group">
                                <span class="input-group-addon">结束日期</span>
                                <input type="text" class="form-control js-datepicker" id="end_date" value="{$endDate}" data-date-format="yyyy-mm-dd">
                            </div>
                            <button type="button" class="btn btn-primary" id="btn_search">查询</button>
                        </div>
                        
                        <!-- 工程师筛选 -->
                        <div class="form-inline" style="display:inline-block;margin-left:10px;">
                            <div class="input-group">
                                <span class="input-group-addon">工程师</span>
                                <select class="form-control" id="engineer_id">
                                    <option value="">全部工程师</option>
                                    {volist name="engineers" id="engineer"}
                                    <option value="{$engineer.id}">{$engineer.name}</option>
                                    {/volist}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 表格 -->
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           width="100%">
                    </table>
                    
                    <!-- 统计汇总 -->
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h3 class="panel-title">汇总信息</h3>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="info-box">
                                        <span class="info-box-icon bg-aqua"><i class="fa fa-list"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">总工单数</span>
                                            <span class="info-box-number" id="total_orders">0</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="info-box">
                                        <span class="info-box-icon bg-green"><i class="fa fa-check"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">已完工数量</span>
                                            <span class="info-box-number" id="completed_count">0</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="info-box">
                                        <span class="info-box-icon bg-red"><i class="fa fa-times"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">已取消数量</span>
                                            <span class="info-box-number" id="canceled_count">0</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="info-box">
                                        <span class="info-box-icon bg-yellow"><i class="fa fa-money"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">结算总金额</span>
                                            <span class="info-box-number" id="total_amount">0.00</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(function () {
        // 初始化表格
        var table = $("#table");
        table.bootstrapTable({
            url: '{:url("salary/index")}',
            pk: 'engineer_name',
            sortName: 'total_amount',
            sortOrder: 'desc',
            toolbar: '#toolbar',
            columns: [
                {field: 'engineer_name', title: '工程师姓名', sortable: true},
                {field: 'total_orders', title: '总工单数', sortable: true},
                {field: 'completed_count', title: '已完工数量', sortable: true},
                {field: 'canceled_count', title: '已取消数量', sortable: true},
                {field: 'total_amount', title: '结算总金额(元)', sortable: true, 
                 formatter: function(value) {
                     return parseFloat(value).toFixed(2);
                 }
                },
                {field: 'avg_amount', title: '平均结算金额(元)', sortable: true, 
                 formatter: function(value) {
                     return parseFloat(value).toFixed(2);
                 }
                },
                {field: 'operate', title: '操作', table: table, 
                 buttons: [
                     {
                         name: 'detail', 
                         text: '详细', 
                         icon: 'fa fa-eye', 
                         classname: 'btn btn-xs btn-primary',
                         url: 'salary/detail?engineer={engineer_name}'
                     }
                 ], 
                 formatter: Table.api.formatter.operate
                }
            ],
            onLoadSuccess: function (data) {
                // 更新统计数据
                if(data.summary) {
                    $("#total_orders").text(data.summary.total_orders);
                    $("#completed_count").text(data.summary.completed_count);
                    $("#canceled_count").text(data.summary.canceled_count);
                    $("#total_amount").text(parseFloat(data.summary.total_amount).toFixed(2));
                }
            }
        });
        
        // 初始化日期选择器
        $('.js-datepicker').datetimepicker({
            format: 'yyyy-mm-dd',
            language: "zh-CN",
            minView: 2,
            autoclose: true
        });
        
        // 搜索按钮事件
        $("#btn_search").on('click', function () {
            table.bootstrapTable('refresh', {
                query: {
                    start_date: $("#start_date").val(),
                    end_date: $("#end_date").val(),
                    engineer_id: $("#engineer_id").val()
                }
            });
        });
        
        // 工程师筛选变更事件
        $("#engineer_id").on('change', function () {
            table.bootstrapTable('refresh', {
                query: {
                    start_date: $("#start_date").val(),
                    end_date: $("#end_date").val(),
                    engineer_id: $(this).val()
                }
            });
        });
        
        // 导出按钮事件
        $(".btn-export").on('click', function () {
            var url = "{:url('salary/export')}" + 
                "?start_date=" + $("#start_date").val() + 
                "&end_date=" + $("#end_date").val() + 
                "&engineer_id=" + $("#engineer_id").val();
            window.location.href = url;
        });
    });
</script>