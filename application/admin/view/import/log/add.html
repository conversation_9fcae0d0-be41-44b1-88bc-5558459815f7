<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <input class="form-control" name="row[update]" type="hidden" value="{$update}">
    <input class="form-control" name="row[to]" type="hidden" value="{$to}">

    <div class="panel panel-default panel-intro">
        <div class="panel-heading">
            <div class="panel-lead"><em>导入参数</em></div>
            <div class="row">
                <div class="col-xs-12 col-sm-6 col-md-6">
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-3">{:__('Table')}:</label>
                        <div class="col-xs-12 col-sm-8" id="selectable">
                            {empty name="table"} 
                            <input id="c-table" class="form-control" name="row[table]" type="hidden" value="fa_order">
                            <span id="table-display">fa_order</span>
                            <select id="selectable-select" style="display:none;"><option value="fa_order" selected>fa_order</option></select>
                            {else /}
                            <input id="c-table" class="form-control" name="row[table]" type="hidden" value="fa_order">
                            <span id="table-display">fa_order</span>
                            <select id="selectable-select" style="display:none;"><option value="fa_order" selected>fa_order</option></select>
                            {/empty}
                        </div>
                    </div>
                </div>

                <div class="col-xs-12 col-sm-6 col-md-6">
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-3">{:__('Row')}:</label>
                        <div class="col-xs-12 col-sm-8">
                            <input id="c-row" class="form-control" name="row[row]" type="number" value=2>
                            <span id="helpBlock" class="help-block">该行的上一行,为匹配行</span>
                        </div>
                    </div>
                </div>
                <div class="col-xs-12 col-sm-6 col-md-6">

                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-3">{:__('Head_type')}:</label>
                        <div class="col-xs-12 col-sm-8" id="selecthead_type">
                            {:Form::select('row[head_type]', ['comment'=>'注释', 'name'=>'字段名'], 'comment', ['data-rule'=>'required'])}
                            <span id="helpBlock" class="help-block"></span>
                        </div>
                    </div>

                </div>
                <div class="col-xs-12 col-sm-6 col-md-6">

                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-3">{:__('path')}:</label>
                        <div class="col-xs-12 col-sm-8">
                            {:Form::upload('row[path]', '', ['data-rule'=>'required'])}
                        </div>
                    </div>

                </div>
            </div>


            <div class="hidden">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2">{:__('NewTable')}:</label>
                    <div class="col-xs-12 col-sm-8">
                        <input id="c-newtable" class="form-control" name="row[newtable]" placeholder="不新建則留空，不需要前缀" type="text">
                    </div>
                </div>
            </div>


        </div>

        <div class="panel-body">


            <div class="panel-heading" style="padding:10px 0">
                <ul class="nav nav-tabs">
                    <li class="active"><a href="#one" data-value="" data-toggle="tab">{:__('匹配结果')}</a></li>

                </ul>
            </div>
            <div class="tab-content">
                <div class="tab-pane fade active in" id="one">
                    <div class="widget-body no-padding">
                        <table id="tableset" class="table table-striped table-bordered table-hover table-nowrap" width="100%">
                            <!--  <p class="text-center" style="font-size: 18px;"> <span class="fa fa-wrench"></span> 请设置导入参数</p>-->
                        </table>


                    </div>
                </div>
            </div>
        </div>
    </div>

    <input id="step" type="hidden" name="row[step]" type="number" value=0>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <div class="hidden"><button type="submit" id="submit" class="btn btn-success btn-embossed">{:__('预览')}</button></div>
            <button id="import" class="btn btn-success disable">{:__('开始导入')}</button>
        </div>
    </div>




</form>












<style>
    select.input-sm {
        height: 28px;
        line-height: 15px;
    }
    
    .popover-content {
        padding: 9px 29px;
    }
    
    .content {
        padding: 0;
        margin: 0;
    }
</style>