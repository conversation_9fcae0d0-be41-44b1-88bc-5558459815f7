<?php
namespace app\api\controller;

use app\common\controller\Api;
use think\facade\Log;

class Csp extends Api
{
    protected $noNeedLogin = ['forwardOrderList'];
    protected $noNeedRight = ['*'];

    /**
     * 工单列表接口（完全还原CURL请求）
     * 
     * @ApiMethod(POST)
     */
    public function forwardOrderList()
    {
        // 1. 目标API地址（与原始CURL完全一致）
        $apiUrl = 'https://anapi.annto.com/api-csp-admin/csp/head/forwardOrderList?pageNum=1&pageSize=50';

        // 2. 准备Headers（完全保留原始所有请求头）
        $headers = [
            'Accept: application/json, text/plain, */*',
            'Accept-Language: zh-CN,zh;q=0.9',
            'Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjQ5NGRkNWYzLWIyMmUtNDMyYS1iNDljLWM4NjQ4YmU3NDRlNSJ9.1slGuCsuuAQ4UNEaS5UHHgfARariKml3LCElW3foXvshbQQv5A5Tmftv8G3eddftlMwoRHg90O3Kun2DXBJvUQ',
            'Connection: keep-alive',
            'Content-Type: application/json;charset=UTF-8',
            'Cookie: MAS_TGC=eyJhbGciOiJIUzUxMiJ9.WlhsS05tRllRV2xQYVVwRlVsVlphVXhEU21oaVIyTnBUMmxLYTJGWVNXbE1RMHBzWW0xTmFVOXBTa0pOVkVrMFVUQktSRXhWYUZSTmFsVXlTVzR3TGk1Nk9EaGpiRzh0WTFjMVMwZEphMlJOV21ZM04xQjNMamRPYzBSbFVIbGFNVzVFY1RsWWJIZG9TV2xhY25aZk1HTXROM0pqUjBSTVkwUk5TVmxqU0hGQ1VEbEJiVU41WjJ4eVZXWnZVemRsT0ZCa01WaFlRemx2VEcxcFIxWndSek5qU3pBeU5VSkVabkozYW5wdlQwZEJObEZyWkhwUVMwWmZMVzlQZWxwSWJFbE9XalJQYm1oM2MyTnJOMWxGV2t0T1F6RTFWbFV3TXpKM05WbDRaRVZtU1VScVVFOTZNbGhNV0U0MlQzTTFZMWRHVTNGTlZVNXpjbkJNV0ZCNmExbFVaeTVtYjJSUWJIVmxRazFZTlRreFJGbHZORFZSWmtWUg.TFOjG8VQs1ZX2T19s8f95Jx8Hh0aqsx4RDo1hKGvSkwXQHNuIMHHl6Mcy1mBiw8PHjRgDhglB_VWX2qnf9OO4g; apmUser=aw3301025022; midea_auth=062059FE94976F527383AFA7AB86764AF892EADAEB7D939A47C9EB03B3181D59EF3B3CCFDCE9F7E0407B3027FFC07F8A60B1930F449391DE17C1F38CA6F95312823CA6A136781494928DD56A842F2C02263EE312561E59CA07EF0F9841586E61AFE8FFAFAF8313308A8B66F8B7C2148D5A9774F128403615FFF8D4C63B0D21F754BCAC895B8F161E4716F44A26524047A343E518924E4456C2CEB23EF9E64B526C45B7552F389AFAA0766209E4BA6710504735E99C024A16C787A59C3C16C55851DC31DF4CD1DB735386C7D4F5BB84F67FE17894E43105A37BAC7DD04D0F183EA912B80889DFF4C7C359A1B105DA587C24FD89DC02BBDF11CDAC5045AC91A650; JSESSIONID=86B9C72534C7D4DF4B6A9D1B3251F490',
            'Origin: https://xiaoan.annto.com',
            'Referer: https://xiaoan.annto.com/csp/indexcsp.html',
            'Sec-Fetch-Dest: empty',
            'Sec-Fetch-Mode: cors',
            'Sec-Fetch-Site: same-site',
            'T-UAP-APPLICATIONCODE: APP202104220005',
            'T-UAP-RESOURCECODE: RE1202105150061',
            'User-Agent: Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.6834.83 Safari/537.36',
            'sec-ch-ua: "Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
            'sec-ch-ua-mobile: ?0',
            'sec-ch-ua-platform: "Windows"'
        ];

        // 3. 准备POST数据（完全保留原始JSON结构）
        $postData = [
            'pageNum' => 1,
            'pageSize' => 50,
            'workNo' => "",
            'information' => "",
            'createDate' => ["2025-03-15 00:00:00","2025-04-14 23:59:59"],
            'logisticsStatusS' => ["30","50","55","60","70","75"],
            'serviceStatusS' => [],
            'organizationss' => null,
            'receiverfficeArea' => [],
            'receiverProvinceCode' => null,
            'receiverProvinceName' => null,
            'receiverCityCode' => null,
            'receiverCityName' => null,
            'receiverDistrictCode' => null,
            'receiverDistrictName' => null,
            'receiverTownCode' => null,
            'receiverTownName' => null,
            'serverNameCn' => "",
            'siteName' => "",
            'waybillNo' => "",
            'goodsReturnNo' => "",
            'subWaybillNo' => "",
            'senderMobile' => "",
            'senderName' => "",
            'receiverName' => "",
            'receiverDetailAddr' => "",
            'orderSourcePlatformList' => [],
            'reverseFlag' => 0,
            'orderStatusCombinationList' => null,
            'workSubStateList' => null,
            'workTypeList' => null,
            'orderTypeList' => null,
            'customerName' => null,
            'businessModeList' => null,
            'deliveryTypeList' => null,
            'workSubState' => null,
            'workType' => null,
            'deliverEngineerName' => null,
            'installEngineerName' => null,
            'initFinalSignPlanTimes' => null,
            'appointmentFlag' => null,
            'dispatchingFlag' => null,
            'userSignTime' => null,
            'userDate' => null,
            'networkSignTime' => null,
            'finishTime' => null,
            'appointmentTime' => null,
            'networkArrivePlanTime' => null,
            'networkSignTimeWhere' => null,
            'networkArrivePlanTimeWhere' => null,
            'networkSignPlanTimeWhere' => null,
            'itemClassName' => null,
            'authBrand' => null,
            'terminalServicePlatformName' => null,
            'lastKilometer' => null,
            'sourceSystemList' => null,
            'interceptFlag' => null,
            'sendLoadingCenter' => null,
            'jpOrderNo' => null,
            'platformOrderNo' => null,
            'completionDelivery' => null,
            'completionInstall' => null,
            'oldnewFlag' => null,
            'isAppointment' => null,
            'completionReverse' => null,
            'tsFlag' => null,
            'businessCategorys' => null,
            'pickAddrName' => null,
            'itemCategoryCodeList' => null,
            'brandList' => null,
            'distributionInstallFlag' => null,
            'orderLabels' => null,
            'customerCodeList' => null,
            'whCodeList' => null,
            'networkOutPlanTimeStart' => null,
            'networkOutPlanTimeEnd' => null,
            'queryWhEndDate' => null,
            'whEndCategoryList' => null,
            'orderNoOrServiceOrderNoValue' => "",
            'orderNoOrServiceOrderNoContent' => [],
            'receiverMobile' => ""
        ];

        // 4. 发送请求
        $response = $this->sendExactCurlRequest($apiUrl, $headers, $postData);
        
        // 5. 返回原始响应
        return json(json_decode($response, true));
    }

    /**
     * 精确发送CURL请求（私有方法）
     */
    private function sendExactCurlRequest($url, $headers, $data)
    {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);

        $response = curl_exec($ch);
        if(curl_errno($ch)){
            Log::error('CURL Error: '.curl_error($ch));
        }
        curl_close($ch);
        
        return $response;
    }
}