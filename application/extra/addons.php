<?php

return [
    'autoload' => false,
    'hooks' => [
        'upgrade' => [
            'department',
            'famysql',
        ],
        'app_begin' => [
            'department',
        ],
        'app_init' => [
            'log',
            'qrcode',
        ],
        'prismhook' => [
            'prism',
        ],
        'config_init' => [
            'summernote',
        ],
    ],
    'route' => [
        '/qrcode$' => 'qrcode/index/index',
        '/qrcode/build$' => 'qrcode/index/build',
    ],
    'priority' => [],
    'domain' => '',
];
