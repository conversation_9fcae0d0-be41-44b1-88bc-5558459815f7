<div id="content-container" class="container">
    <div class="user-section login-section">
        <div class="logon-tab clearfix"><a class="active">{:__('Sign in')}</a></div>
        <div class="login-main">
            <form name="form" id="login-form" class="form-vertical" method="POST" action="">
                <input type="hidden" name="url" value="{$url|htmlentities}"/>
                {:token()}
                <div class="form-group">
                    <label class="control-label" for="mobile">手机号</label>
                    <div class="controls">
                        <input class="form-control" id="mobile" type="text" name="mobile" value="" data-rule="required;mobile" placeholder="请输入手机号" autocomplete="off">
                        <div class="help-block"></div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label" for="password">{:__('Password')}</label>
                    <div class="controls">
                        <input class="form-control" id="password" type="password" name="password" data-rule="required;password" placeholder="{:__('Password')}" autocomplete="off">
                    </div>
                </div>
                <div class="form-group">
                    <div class="controls">
                        <div class="checkbox inline">
                            <label>
                                <input type="checkbox" name="keeplogin" checked="checked" value="1"> {:__('Keep login')}
                            </label>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-primary btn-lg btn-block">{:__('Sign in')}</button>
                </div>
            </form>
        </div>
    </div>
</div>
<script>
    $(function () {
        // 表单验证
        $('#login-form').validator({
            fields: {
                'mobile': '手机号:required;mobile',
                'password': '密码:required;length(6~50)'
            },
            valid: function (form) {
                // 表单验证成功时提交
                $(form).ajaxSubmit({
                    dataType: 'json',
                    success: function (data, statusText, xhr, $form) {
                        if (data.code === 1) {
                            location.href = data.url ? data.url : '/';
                        } else {
                            Layer.alert(data.msg);
                            $form.find(':submit').prop('disabled', false);
                            $form.find('.form-group').removeClass('has-feedback has-success');
                            $form.find('.help-block').html('');
                        }
                    },
                    error: function (data, statusText, xhr, $form) {
                        Layer.alert('服务器错误');
                        $form.find(':submit').prop('disabled', false);
                    }
                });
                return false;
            }
        });
    });
</script>
