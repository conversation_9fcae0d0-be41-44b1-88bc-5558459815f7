version: '3.8'

services:
  # Web应用服务
  web:
    build: .
    container_name: smart-dispatch-web
    ports:
      - "8080:80"
    volumes:
      - ./public/uploads:/var/www/html/public/uploads
      - ./runtime:/var/www/html/runtime
    environment:
      - APP_DEBUG=false
      - DATABASE_TYPE=mysql
      - DATABASE_HOSTNAME=mysql
      - DATABASE_DATABASE=smart_dispatch
      - DATABASE_USERNAME=root
      - DATABASE_PASSWORD=123456
      - DATABASE_PREFIX=fa_
    depends_on:
      - mysql
    networks:
      - smart-dispatch-network

  # MySQL数据库服务
  mysql:
    image: mysql:8.0
    container_name: smart-dispatch-mysql
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: smart_dispatch
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - smart-dispatch-network

  # Redis缓存服务（可选）
  redis:
    image: redis:7-alpine
    container_name: smart-dispatch-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - smart-dispatch-network

  # phpMyAdmin数据库管理工具（可选）
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: smart-dispatch-phpmyadmin
    ports:
      - "8081:80"
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: 123456
    depends_on:
      - mysql
    networks:
      - smart-dispatch-network

volumes:
  mysql_data:
  redis_data:

networks:
  smart-dispatch-network:
    driver: bridge
