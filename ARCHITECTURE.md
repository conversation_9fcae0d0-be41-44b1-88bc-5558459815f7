# 智慧派单管理系统 - 项目架构文档

## 项目概述

智慧派单管理系统是一个基于 FastAdmin 框架开发的企业级派单管理平台，专为家居配送安装服务设计。系统采用现代化的 Web 技术栈，提供完整的订单管理、工程师派单、工单跟踪和结算管理功能。

## 技术架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                        前端层 (Frontend)                      │
├─────────────────────────────────────────────────────────────┤
│  jQuery + Bootstrap + Layer + Toastr + 自定义组件            │
│  - 响应式UI界面                                              │
│  - AJAX异步交互                                              │
│  - 实时数据更新                                              │
│  - 二维码生成/扫描                                           │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                        应用层 (Application)                   │
├─────────────────────────────────────────────────────────────┤
│                    FastAdmin Framework                       │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │   Admin     │  │    API      │  │   Index     │          │
│  │  后台管理    │  │   接口层     │  │  前台展示    │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                Common 公共模块                           │ │
│  │  - 工具类库  - 验证器  - 中间件  - 异常处理               │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                        框架层 (Framework)                     │
├─────────────────────────────────────────────────────────────┤
│                      ThinkPHP 5.x                           │
│  - MVC架构模式                                               │
│  - ORM数据库操作                                             │
│  - 路由管理                                                  │
│  - 缓存系统                                                  │
│  - 队列处理                                                  │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                        数据层 (Database)                      │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │   MySQL     │  │   Redis     │  │  File Cache │          │
│  │  主数据库    │  │   缓存      │  │  文件缓存    │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

### 技术栈详情

#### 后端技术栈
- **PHP**: >= 7.4.0
- **框架**: ThinkPHP 5.x + FastAdmin
- **数据库**: MySQL 8.0
- **缓存**: Redis (可选)
- **Web服务器**: Apache 2.4
- **包管理**: Composer

#### 前端技术栈
- **基础库**: jQuery 3.7.1
- **UI框架**: Bootstrap 3.4.1
- **弹窗组件**: Layer 3.5.6
- **提示组件**: Toastr 2.1.3
- **表格组件**: Bootstrap Table
- **日期组件**: Bootstrap DateRangePicker
- **构建工具**: Grunt

#### 开发工具
- **容器化**: Docker + Docker Compose
- **版本控制**: Git
- **代码规范**: PSR-4 自动加载

## 目录结构

```
smart-dispatch-system/
├── application/                 # 应用目录
│   ├── admin/                  # 后台管理模块
│   │   ├── controller/         # 控制器
│   │   ├── model/             # 模型
│   │   ├── view/              # 视图
│   │   ├── validate/          # 验证器
│   │   └── command/           # 命令行工具
│   ├── api/                   # API接口模块
│   │   ├── controller/        # API控制器
│   │   └── model/            # API模型
│   ├── index/                 # 前台模块
│   │   ├── controller/        # 前台控制器
│   │   ├── model/            # 前台模型
│   │   └── view/             # 前台视图
│   ├── common/                # 公共模块
│   │   ├── controller/        # 公共控制器
│   │   ├── model/            # 公共模型
│   │   ├── library/          # 类库
│   │   └── view/             # 公共视图
│   ├── config.php             # 应用配置
│   ├── database.php           # 数据库配置
│   ├── route.php              # 路由配置
│   └── tags.php               # 行为配置
├── public/                    # Web根目录
│   ├── index.php              # 入口文件
│   ├── assets/                # 静态资源
│   │   ├── css/              # 样式文件
│   │   ├── js/               # JavaScript文件
│   │   ├── img/              # 图片资源
│   │   └── libs/             # 第三方库
│   ├── uploads/               # 上传文件
│   └── template/              # 模板资源
├── thinkphp/                  # ThinkPHP框架
├── vendor/                    # Composer依赖
├── runtime/                   # 运行时文件
│   ├── cache/                # 缓存文件
│   ├── log/                  # 日志文件
│   └── temp/                 # 临时文件
├── extend/                    # 扩展类库
├── addons/                    # 插件目录
├── docker/                    # Docker配置
│   ├── apache-config.conf     # Apache配置
│   ├── mysql/                # MySQL配置
│   ├── build.sh              # 构建脚本
│   └── README.md             # Docker文档
├── composer.json              # PHP依赖配置
├── package.json               # 前端依赖配置
├── Dockerfile                 # Docker镜像配置
├── docker-compose.yml         # Docker编排配置
├── Makefile                   # 构建命令
└── README.md                  # 项目说明
```

## 核心模块设计

### 1. 订单管理模块
- **功能**: 订单导入、查询、编辑、状态管理
- **技术**: ThinkPHP Model + MySQL
- **特性**: 支持批量操作、条件筛选、数据导出

### 2. 工程师管理模块
- **功能**: 工程师信息管理、区域分配、技能标签
- **技术**: 关联查询 + 地理位置匹配
- **特性**: 智能派单算法、负载均衡

### 3. 工单处理模块
- **功能**: 工单状态跟踪、批量更新、实时通知
- **技术**: AJAX + WebSocket (可选)
- **特性**: 状态机管理、操作日志记录

### 4. 结算系统模块
- **功能**: 结算金额计算、统计分析、财务报表
- **技术**: 数据聚合 + 图表展示
- **特性**: 安全验证、审计追踪

### 5. 二维码系统
- **功能**: 运单二维码生成、扫描识别
- **技术**: QR Code API + 移动端适配
- **特性**: 离线扫描、批量生成

## 数据库设计

### 核心数据表

```sql
-- 订单表
fa_orders
├── id (主键)
├── order_no (订单号)
├── customer_info (客户信息)
├── product_info (产品信息)
├── address (配送地址)
├── status (订单状态)
├── engineer_id (工程师ID)
├── amount (金额)
├── create_time (创建时间)
└── update_time (更新时间)

-- 工程师表
fa_engineers
├── id (主键)
├── name (姓名)
├── mobile (手机号)
├── region (服务区域)
├── skills (技能标签)
├── status (状态)
├── create_time (创建时间)
└── update_time (更新时间)

-- 工单表
fa_work_orders
├── id (主键)
├── order_id (订单ID)
├── engineer_id (工程师ID)
├── status (工单状态)
├── start_time (开始时间)
├── finish_time (完成时间)
├── remarks (备注)
├── create_time (创建时间)
└── update_time (更新时间)

-- 结算表
fa_settlements
├── id (主键)
├── order_id (订单ID)
├── engineer_id (工程师ID)
├── amount (结算金额)
├── status (结算状态)
├── settle_time (结算时间)
├── create_time (创建时间)
└── update_time (更新时间)
```

## 部署架构

### Docker 容器化部署

```
┌─────────────────────────────────────────────────────────────┐
│                        负载均衡层                             │
│                    Nginx (可选)                             │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                        应用容器                              │
│                  smart-dispatch-web                         │
│                  PHP 8.1 + Apache                          │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                        数据层                                │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │  MySQL 容器      │    │   Redis 容器     │                │
│  │  数据持久化      │    │   缓存加速       │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
```

### 生产环境建议

1. **高可用部署**
   - 多实例负载均衡
   - 数据库主从复制
   - Redis 集群模式

2. **安全配置**
   - HTTPS 证书配置
   - 防火墙规则设置
   - 数据库访问限制

3. **监控告警**
   - 应用性能监控
   - 数据库性能监控
   - 磁盘空间监控

## 性能优化

### 1. 数据库优化
- 索引优化
- 查询优化
- 连接池配置
- 读写分离

### 2. 缓存策略
- Redis 数据缓存
- 页面静态化
- CDN 加速
- 浏览器缓存

### 3. 前端优化
- 资源压缩
- 图片优化
- 异步加载
- 懒加载技术

## 安全设计

### 1. 数据安全
- SQL 注入防护
- XSS 攻击防护
- CSRF 令牌验证
- 数据加密存储

### 2. 访问控制
- 用户权限管理
- 角色权限控制
- API 访问限制
- 操作日志记录

### 3. 系统安全
- 文件上传限制
- 目录访问控制
- 错误信息隐藏
- 安全头配置

## 扩展性设计

### 1. 模块化设计
- 插件机制
- 钩子系统
- 事件驱动
- 服务解耦

### 2. API 设计
- RESTful 接口
- 版本控制
- 文档自动生成
- 接口测试

### 3. 微服务准备
- 服务拆分规划
- 数据库分离
- 消息队列集成
- 分布式缓存

---

© 2025 智慧派单管理系统 - 架构设计文档
