# Git相关
.git
.gitignore
.gitattributes

# 文档
README.md
*.md

# 开发工具配置
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
*.log
logs/

# 运行时文件
runtime/log/*
runtime/cache/*
runtime/temp/*

# 上传文件（保留目录结构）
public/uploads/*
!public/uploads/.gitkeep

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Composer
vendor/
composer.lock

# 系统文件
.DS_Store
Thumbs.db

# 临时文件
*.tmp
*.temp

# 备份文件
*.bak
*.backup

# 测试文件
tests/
phpunit.xml

# Docker相关
Dockerfile
docker-compose.yml
.dockerignore

# 环境配置
.env
.env.local
.env.production
