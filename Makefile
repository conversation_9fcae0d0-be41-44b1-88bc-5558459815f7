# 智慧派单管理系统 Makefile
# 提供便捷的 Docker 操作命令

# 项目配置
PROJECT_NAME = smart-dispatch-system
IMAGE_NAME = smart-dispatch
VERSION = 1.0.0

# 颜色定义
RED = \033[0;31m
GREEN = \033[0;32m
YELLOW = \033[1;33m
BLUE = \033[0;34m
NC = \033[0m

.PHONY: help build up down restart logs status clean install dev prod backup restore

# 默认目标
help: ## 显示帮助信息
	@echo "$(BLUE)智慧派单管理系统 - Docker 管理命令$(NC)"
	@echo ""
	@echo "$(YELLOW)可用命令:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

build: ## 构建 Docker 镜像
	@echo "$(BLUE)构建 Docker 镜像...$(NC)"
	docker-compose build
	@echo "$(GREEN)✓ 镜像构建完成$(NC)"

up: ## 启动所有服务
	@echo "$(BLUE)启动服务...$(NC)"
	docker-compose up -d
	@echo "$(GREEN)✓ 服务启动成功$(NC)"
	@echo ""
	@echo "$(YELLOW)服务访问地址:$(NC)"
	@echo "  Web应用: http://localhost:8080"
	@echo "  phpMyAdmin: http://localhost:8081"

down: ## 停止所有服务
	@echo "$(BLUE)停止服务...$(NC)"
	docker-compose down
	@echo "$(GREEN)✓ 服务已停止$(NC)"

restart: ## 重启所有服务
	@echo "$(BLUE)重启服务...$(NC)"
	docker-compose restart
	@echo "$(GREEN)✓ 服务重启完成$(NC)"

logs: ## 查看服务日志
	@echo "$(BLUE)查看服务日志 (按 Ctrl+C 退出):$(NC)"
	docker-compose logs -f

status: ## 查看服务状态
	@echo "$(BLUE)服务状态:$(NC)"
	docker-compose ps

clean: ## 清理容器和镜像
	@echo "$(YELLOW)警告: 这将删除所有相关的容器、镜像和数据卷！$(NC)"
	@read -p "确定要继续吗? (y/N): " confirm && [ "$$confirm" = "y" ] || exit 1
	@echo "$(BLUE)清理中...$(NC)"
	docker-compose down -v
	docker rmi $(IMAGE_NAME):$(VERSION) $(IMAGE_NAME):latest 2>/dev/null || true
	docker system prune -f
	@echo "$(GREEN)✓ 清理完成$(NC)"

install: build up ## 完整安装 (构建 + 启动)
	@echo "$(GREEN)✓ 安装完成！$(NC)"
	@echo ""
	@echo "$(YELLOW)默认管理员账号:$(NC)"
	@echo "  用户名: admin"
	@echo "  密码: 123456"

dev: ## 开发模式启动
	@echo "$(BLUE)以开发模式启动...$(NC)"
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
	@echo "$(GREEN)✓ 开发环境启动成功$(NC)"

prod: ## 生产模式启动
	@echo "$(BLUE)以生产模式启动...$(NC)"
	docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
	@echo "$(GREEN)✓ 生产环境启动成功$(NC)"

backup: ## 备份数据库
	@echo "$(BLUE)备份数据库...$(NC)"
	@mkdir -p backups
	docker exec smart-dispatch-mysql mysqldump -u root -p123456 smart_dispatch > backups/backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "$(GREEN)✓ 数据库备份完成$(NC)"

restore: ## 恢复数据库 (需要指定备份文件: make restore FILE=backup.sql)
	@if [ -z "$(FILE)" ]; then \
		echo "$(RED)错误: 请指定备份文件，例如: make restore FILE=backup.sql$(NC)"; \
		exit 1; \
	fi
	@echo "$(BLUE)恢复数据库...$(NC)"
	docker exec -i smart-dispatch-mysql mysql -u root -p123456 smart_dispatch < $(FILE)
	@echo "$(GREEN)✓ 数据库恢复完成$(NC)"

shell-web: ## 进入 Web 容器
	docker exec -it smart-dispatch-web bash

shell-mysql: ## 进入 MySQL 容器
	docker exec -it smart-dispatch-mysql bash

shell-redis: ## 进入 Redis 容器
	docker exec -it smart-dispatch-redis redis-cli

update: ## 更新应用 (拉取代码 + 重新构建)
	@echo "$(BLUE)更新应用...$(NC)"
	git pull
	docker-compose down
	docker-compose build --no-cache
	docker-compose up -d
	@echo "$(GREEN)✓ 应用更新完成$(NC)"

test: ## 运行测试
	@echo "$(BLUE)运行测试...$(NC)"
	docker-compose exec web php think unit
	@echo "$(GREEN)✓ 测试完成$(NC)"

# 快捷命令别名
start: up ## 启动服务 (up 的别名)
stop: down ## 停止服务 (down 的别名)
rebuild: ## 重新构建并启动
	@echo "$(BLUE)重新构建并启动...$(NC)"
	docker-compose down
	docker-compose build --no-cache
	docker-compose up -d
	@echo "$(GREEN)✓ 重新构建完成$(NC)"
