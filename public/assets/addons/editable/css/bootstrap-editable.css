/*! X-editable - v1.5.1
* In-place editing with Twitter Bootstrap, jQuery UI or pure jQuery
* http://github.com/vitalets/x-editable
* Copyright (c) 2013 <PERSON><PERSON><PERSON>v; Licensed MIT */
/*
  BS3 width:1005 for inputs breaks editable form in popup
  See: https://github.com/vitalets/x-editable/issues/393
*/
/*for jquery-ui buttons need set height to look more pretty*/
/*add padding for jquery ui*/
/* ---- For specific types ---- */
/* move datepicker icon to center of add-on button. See https://github.com/vitalets/x-editable/issues/183 */
/* checklist vertical alignment */
/* set exact width of textarea to fit buttons toolbar */
/* clear button shown as link in date inputs */
/* IOS-style clear button for text inputs */
/*see https://github.com/vitalets/x-editable/issues/139 */
/*!
 * Datepicker for Bootstrap
 *
 * Copyright 2012 Stefan Petre
 * Improvements by <PERSON>
 * Licensed under the Apache License v2.0
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 */
.editableform {
  margin-bottom: 0;
}
.editableform .control-group {
  margin-bottom: 0;
  white-space: nowrap;
  line-height: 20px;
}
.editableform .form-control {
  width: auto;
}
.editableform .editable-date {
  padding: 0;
  margin: 0;
  float: left;
}
.editable-buttons {
  display: inline-block;
  vertical-align: top;
  margin-left: 7px;
  zoom: 1;
  *display: inline;
}
.editable-buttons .editable-cancel {
  margin-left: 7px;
}
.editable-buttons button.ui-button-icon-only {
  height: 24px;
  width: 30px;
}
.editable-buttons.editable-buttons-bottom {
  display: block;
  margin-top: 7px;
  margin-left: 0;
}
.editable-input {
  vertical-align: top;
  display: inline-block;
  width: auto;
  white-space: normal;
  zoom: 1;
  *display: inline;
}
.editableform-loading {
  background: url('../img/loading.gif') center center no-repeat;
  height: 25px;
  width: auto;
  min-width: 25px;
}
.editable-inline .editableform-loading {
  background-position: left 5px;
}
.editable-inline .add-on .icon-th {
  margin-top: 3px;
  margin-left: 1px;
}
.editable-error-block {
  max-width: 300px;
  margin: 5px 0 0 0;
  width: auto;
  white-space: normal;
}
.editable-error-block.ui-state-error {
  padding: 3px;
}
.editable-error {
  color: red;
}
.editable-checklist label {
  white-space: nowrap;
}
.editable-checklist label input[type="checkbox"] {
  vertical-align: middle;
  margin: 0;
}
.editable-checklist label span {
  vertical-align: middle;
  margin: 0;
}
.editable-wysihtml5 {
  width: 566px;
  height: 250px;
}
.editable-clear {
  clear: both;
  font-size: 0.9em;
  text-decoration: none;
  text-align: right;
}
.editable-clear-x {
  background: url('../img/clear.png') center center no-repeat;
  display: block;
  width: 13px;
  height: 13px;
  position: absolute;
  opacity: 0.6;
  z-index: 100;
  top: 50%;
  right: 6px;
  margin-top: -6px;
}
.editable-clear-x:hover {
  opacity: 1;
}
.editable-pre-wrapped {
  white-space: pre-wrap;
}
.editable-container.editable-popup {
  max-width: none !important;
}
.editable-container.popover {
  width: auto;
}
.editable-container.editable-inline {
  display: inline-block;
  vertical-align: middle;
  width: auto;
  zoom: 1;
  *display: inline;
}
.editable-container.ui-widget {
  font-size: inherit;
  z-index: 9990;
}
.editable-click {
  text-decoration: none;
  border-bottom: dashed 1px #0088cc;
}
a.editable-click {
  text-decoration: none;
  border-bottom: dashed 1px #0088cc;
}
a.editable-click:hover {
  text-decoration: none;
  border-bottom: dashed 1px #0088cc;
}
.editable-click.editable-disabled {
  color: #585858;
  cursor: default;
  border-bottom: none;
}
a.editable-click.editable-disabled {
  color: #585858;
  cursor: default;
  border-bottom: none;
}
a.editable-click.editable-disabled:hover {
  color: #585858;
  cursor: default;
  border-bottom: none;
}
.editable-empty {
  font-style: italic;
  color: #DD1144;
  text-decoration: none;
}
.editable-empty:hover {
  font-style: italic;
  color: #DD1144;
  text-decoration: none;
}
.editable-empty:focus {
  font-style: italic;
  color: #DD1144;
  text-decoration: none;
}
.editable-unsaved {
  font-weight: bold;
}
.editable-bg-transition {
  -webkit-transition: background-color 1400ms ease-out;
  -moz-transition: background-color 1400ms ease-out;
  -o-transition: background-color 1400ms ease-out;
  -ms-transition: background-color 1400ms ease-out;
  transition: background-color 1400ms ease-out;
}
.form-horizontal .editable {
  padding-top: 5px;
  display: inline-block;
}
.editableform .datepicker {
  padding: 4px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  direction: ltr;
}
.editableform .datepicker > div {
  display: none;
}
.editableform .datepicker table {
  margin: 0;
}
.editableform .datepicker table tr td.day:hover {
  background: #eeeeee;
  cursor: pointer;
}
.editableform .datepicker table tr td.old {
  color: #999999;
}
.editableform .datepicker table tr td.new {
  color: #999999;
}
.editableform .datepicker table tr td.disabled {
  background: none;
  color: #999999;
  cursor: default;
}
.editableform .datepicker table tr td.disabled:hover {
  background: none;
  color: #999999;
  cursor: default;
}
.editableform .datepicker table tr td.today {
  background-color: #fde19a;
  background-image: -moz-linear-gradient(top, #fdd49a, #fdf59a);
  background-image: -ms-linear-gradient(top, #fdd49a, #fdf59a);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#fdd49a), to(#fdf59a));
  background-image: -webkit-linear-gradient(top, #fdd49a, #fdf59a);
  background-image: -o-linear-gradient(top, #fdd49a, #fdf59a);
  background-image: linear-gradient(top, #fdd49a, #fdf59a);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fdd49a', endColorstr='#fdf59a', GradientType=0);
  border-color: #fdf59a #fdf59a #fbed50;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  color: #000;
}
.editableform .datepicker table tr td.today:hover {
  background-color: #fde19a;
  background-image: -moz-linear-gradient(top, #fdd49a, #fdf59a);
  background-image: -ms-linear-gradient(top, #fdd49a, #fdf59a);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#fdd49a), to(#fdf59a));
  background-image: -webkit-linear-gradient(top, #fdd49a, #fdf59a);
  background-image: -o-linear-gradient(top, #fdd49a, #fdf59a);
  background-image: linear-gradient(top, #fdd49a, #fdf59a);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fdd49a', endColorstr='#fdf59a', GradientType=0);
  border-color: #fdf59a #fdf59a #fbed50;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  color: #000;
  background-color: #fdf59a;
}
.editableform .datepicker table tr td.today:hover:hover {
  background-color: #fdf59a;
  color: #000;
}
.editableform .datepicker table tr td.today:hover:active {
  background-color: #fdf59a;
  background-color: #fbf069 \9;
}
.editableform .datepicker table tr td.today:active {
  background-color: #fdf59a;
  background-color: #fbf069 \9;
}
.editableform .datepicker table tr td.today:hover.active {
  background-color: #fdf59a;
  background-color: #fbf069 \9;
}
.editableform .datepicker table tr td.today:hover.disabled {
  background-color: #fdf59a;
}
.editableform .datepicker table tr td.today:hover[disabled] {
  background-color: #fdf59a;
}
.editableform .datepicker table tr td.today.disabled {
  background-color: #fde19a;
  background-image: -moz-linear-gradient(top, #fdd49a, #fdf59a);
  background-image: -ms-linear-gradient(top, #fdd49a, #fdf59a);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#fdd49a), to(#fdf59a));
  background-image: -webkit-linear-gradient(top, #fdd49a, #fdf59a);
  background-image: -o-linear-gradient(top, #fdd49a, #fdf59a);
  background-image: linear-gradient(top, #fdd49a, #fdf59a);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fdd49a', endColorstr='#fdf59a', GradientType=0);
  border-color: #fdf59a #fdf59a #fbed50;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  color: #000;
  background-color: #fdf59a;
}
.editableform .datepicker table tr td.today.disabled:hover {
  background-color: #fde19a;
  background-image: -moz-linear-gradient(top, #fdd49a, #fdf59a);
  background-image: -ms-linear-gradient(top, #fdd49a, #fdf59a);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#fdd49a), to(#fdf59a));
  background-image: -webkit-linear-gradient(top, #fdd49a, #fdf59a);
  background-image: -o-linear-gradient(top, #fdd49a, #fdf59a);
  background-image: linear-gradient(top, #fdd49a, #fdf59a);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fdd49a', endColorstr='#fdf59a', GradientType=0);
  border-color: #fdf59a #fdf59a #fbed50;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  color: #000;
  background-color: #fdf59a;
}
.editableform .datepicker table tr td.today.disabled:hover:hover {
  background-color: #fdf59a;
}
.editableform .datepicker table tr td.today.disabled:hover:active {
  background-color: #fdf59a;
  background-color: #fbf069 \9;
}
.editableform .datepicker table tr td.today.disabled:active {
  background-color: #fdf59a;
  background-color: #fbf069 \9;
}
.editableform .datepicker table tr td.today.disabled:hover.active {
  background-color: #fdf59a;
  background-color: #fbf069 \9;
}
.editableform .datepicker table tr td.today.disabled:hover.disabled {
  background-color: #fdf59a;
}
.editableform .datepicker table tr td.today.disabled:hover[disabled] {
  background-color: #fdf59a;
}
.editableform .datepicker table tr td.today.active {
  background-color: #fdf59a;
  background-color: #fbf069 \9;
}
.editableform .datepicker table tr td.today.active:hover {
  color: #fff;
}
.editableform .datepicker table tr td.today.disabled.active {
  background-color: #fdf59a;
  background-color: #fbf069 \9;
}
.editableform .datepicker table tr td.today.disabled.disabled {
  background-color: #fdf59a;
}
.editableform .datepicker table tr td.today[disabled] {
  background-color: #fdf59a;
}
.editableform .datepicker table tr td.today.disabled[disabled] {
  background-color: #fdf59a;
}
.editableform .datepicker table tr td.range {
  background: #eeeeee;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
.editableform .datepicker table tr td.range:hover {
  background: #eeeeee;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
.editableform .datepicker table tr td.range.disabled {
  background: #eeeeee;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
.editableform .datepicker table tr td.range.disabled:hover {
  background: #eeeeee;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
.editableform .datepicker table tr td.range.today {
  background-color: #f3d17a;
  background-image: -moz-linear-gradient(top, #f3c17a, #f3e97a);
  background-image: -ms-linear-gradient(top, #f3c17a, #f3e97a);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#f3c17a), to(#f3e97a));
  background-image: -webkit-linear-gradient(top, #f3c17a, #f3e97a);
  background-image: -o-linear-gradient(top, #f3c17a, #f3e97a);
  background-image: linear-gradient(top, #f3c17a, #f3e97a);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f3c17a', endColorstr='#f3e97a', GradientType=0);
  border-color: #f3e97a #f3e97a #edde34;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
.editableform .datepicker table tr td.range.today:hover {
  background-color: #f3d17a;
  background-image: -moz-linear-gradient(top, #f3c17a, #f3e97a);
  background-image: -ms-linear-gradient(top, #f3c17a, #f3e97a);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#f3c17a), to(#f3e97a));
  background-image: -webkit-linear-gradient(top, #f3c17a, #f3e97a);
  background-image: -o-linear-gradient(top, #f3c17a, #f3e97a);
  background-image: linear-gradient(top, #f3c17a, #f3e97a);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f3c17a', endColorstr='#f3e97a', GradientType=0);
  border-color: #f3e97a #f3e97a #edde34;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
  background-color: #f3e97a;
}
.editableform .datepicker table tr td.range.today:hover:hover {
  background-color: #f3e97a;
}
.editableform .datepicker table tr td.range.today:hover:active {
  background-color: #f3e97a;
  background-color: #efe24b \9;
}
.editableform .datepicker table tr td.range.today:active {
  background-color: #f3e97a;
  background-color: #efe24b \9;
}
.editableform .datepicker table tr td.range.today:hover.active {
  background-color: #f3e97a;
  background-color: #efe24b \9;
}
.editableform .datepicker table tr td.range.today:hover.disabled {
  background-color: #f3e97a;
}
.editableform .datepicker table tr td.range.today:hover[disabled] {
  background-color: #f3e97a;
}
.editableform .datepicker table tr td.range.today.disabled {
  background-color: #f3d17a;
  background-image: -moz-linear-gradient(top, #f3c17a, #f3e97a);
  background-image: -ms-linear-gradient(top, #f3c17a, #f3e97a);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#f3c17a), to(#f3e97a));
  background-image: -webkit-linear-gradient(top, #f3c17a, #f3e97a);
  background-image: -o-linear-gradient(top, #f3c17a, #f3e97a);
  background-image: linear-gradient(top, #f3c17a, #f3e97a);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f3c17a', endColorstr='#f3e97a', GradientType=0);
  border-color: #f3e97a #f3e97a #edde34;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
  background-color: #f3e97a;
}
.editableform .datepicker table tr td.range.today.disabled:hover {
  background-color: #f3d17a;
  background-image: -moz-linear-gradient(top, #f3c17a, #f3e97a);
  background-image: -ms-linear-gradient(top, #f3c17a, #f3e97a);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#f3c17a), to(#f3e97a));
  background-image: -webkit-linear-gradient(top, #f3c17a, #f3e97a);
  background-image: -o-linear-gradient(top, #f3c17a, #f3e97a);
  background-image: linear-gradient(top, #f3c17a, #f3e97a);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f3c17a', endColorstr='#f3e97a', GradientType=0);
  border-color: #f3e97a #f3e97a #edde34;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
  background-color: #f3e97a;
}
.editableform .datepicker table tr td.range.today.disabled:hover:hover {
  background-color: #f3e97a;
}
.editableform .datepicker table tr td.range.today.disabled:hover:active {
  background-color: #f3e97a;
  background-color: #efe24b \9;
}
.editableform .datepicker table tr td.range.today.disabled:active {
  background-color: #f3e97a;
  background-color: #efe24b \9;
}
.editableform .datepicker table tr td.range.today.disabled:hover.active {
  background-color: #f3e97a;
  background-color: #efe24b \9;
}
.editableform .datepicker table tr td.range.today.disabled:hover.disabled {
  background-color: #f3e97a;
}
.editableform .datepicker table tr td.range.today.disabled:hover[disabled] {
  background-color: #f3e97a;
}
.editableform .datepicker table tr td.range.today.active {
  background-color: #f3e97a;
  background-color: #efe24b \9;
}
.editableform .datepicker table tr td.range.today.disabled.active {
  background-color: #f3e97a;
  background-color: #efe24b \9;
}
.editableform .datepicker table tr td.range.today.disabled.disabled {
  background-color: #f3e97a;
}
.editableform .datepicker table tr td.range.today[disabled] {
  background-color: #f3e97a;
}
.editableform .datepicker table tr td.range.today.disabled[disabled] {
  background-color: #f3e97a;
}
.editableform .datepicker table tr td.selected {
  background-color: #9e9e9e;
  background-image: -moz-linear-gradient(top, #b3b3b3, #808080);
  background-image: -ms-linear-gradient(top, #b3b3b3, #808080);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#b3b3b3), to(#808080));
  background-image: -webkit-linear-gradient(top, #b3b3b3, #808080);
  background-image: -o-linear-gradient(top, #b3b3b3, #808080);
  background-image: linear-gradient(top, #b3b3b3, #808080);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#b3b3b3', endColorstr='#808080', GradientType=0);
  border-color: #808080 #808080 #595959;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}
.editableform .datepicker table tr td.selected:hover {
  background-color: #9e9e9e;
  background-image: -moz-linear-gradient(top, #b3b3b3, #808080);
  background-image: -ms-linear-gradient(top, #b3b3b3, #808080);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#b3b3b3), to(#808080));
  background-image: -webkit-linear-gradient(top, #b3b3b3, #808080);
  background-image: -o-linear-gradient(top, #b3b3b3, #808080);
  background-image: linear-gradient(top, #b3b3b3, #808080);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#b3b3b3', endColorstr='#808080', GradientType=0);
  border-color: #808080 #808080 #595959;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  background-color: #808080;
}
.editableform .datepicker table tr td.selected:hover:hover {
  background-color: #808080;
}
.editableform .datepicker table tr td.selected:hover:active {
  background-color: #808080;
  background-color: #666666 \9;
}
.editableform .datepicker table tr td.selected:active {
  background-color: #808080;
  background-color: #666666 \9;
}
.editableform .datepicker table tr td.selected:hover.active {
  background-color: #808080;
  background-color: #666666 \9;
}
.editableform .datepicker table tr td.selected:hover.disabled {
  background-color: #808080;
}
.editableform .datepicker table tr td.selected:hover[disabled] {
  background-color: #808080;
}
.editableform .datepicker table tr td.selected.disabled {
  background-color: #9e9e9e;
  background-image: -moz-linear-gradient(top, #b3b3b3, #808080);
  background-image: -ms-linear-gradient(top, #b3b3b3, #808080);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#b3b3b3), to(#808080));
  background-image: -webkit-linear-gradient(top, #b3b3b3, #808080);
  background-image: -o-linear-gradient(top, #b3b3b3, #808080);
  background-image: linear-gradient(top, #b3b3b3, #808080);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#b3b3b3', endColorstr='#808080', GradientType=0);
  border-color: #808080 #808080 #595959;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  background-color: #808080;
}
.editableform .datepicker table tr td.selected.disabled:hover {
  background-color: #9e9e9e;
  background-image: -moz-linear-gradient(top, #b3b3b3, #808080);
  background-image: -ms-linear-gradient(top, #b3b3b3, #808080);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#b3b3b3), to(#808080));
  background-image: -webkit-linear-gradient(top, #b3b3b3, #808080);
  background-image: -o-linear-gradient(top, #b3b3b3, #808080);
  background-image: linear-gradient(top, #b3b3b3, #808080);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#b3b3b3', endColorstr='#808080', GradientType=0);
  border-color: #808080 #808080 #595959;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  background-color: #808080;
}
.editableform .datepicker table tr td.selected.disabled:hover:hover {
  background-color: #808080;
}
.editableform .datepicker table tr td.selected.disabled:hover:active {
  background-color: #808080;
  background-color: #666666 \9;
}
.editableform .datepicker table tr td.selected.disabled:active {
  background-color: #808080;
  background-color: #666666 \9;
}
.editableform .datepicker table tr td.selected.disabled:hover.active {
  background-color: #808080;
  background-color: #666666 \9;
}
.editableform .datepicker table tr td.selected.disabled:hover.disabled {
  background-color: #808080;
}
.editableform .datepicker table tr td.selected.disabled:hover[disabled] {
  background-color: #808080;
}
.editableform .datepicker table tr td.selected.active {
  background-color: #808080;
  background-color: #666666 \9;
}
.editableform .datepicker table tr td.selected.disabled.active {
  background-color: #808080;
  background-color: #666666 \9;
}
.editableform .datepicker table tr td.selected.disabled.disabled {
  background-color: #808080;
}
.editableform .datepicker table tr td.selected[disabled] {
  background-color: #808080;
}
.editableform .datepicker table tr td.selected.disabled[disabled] {
  background-color: #808080;
}
.editableform .datepicker table tr td.active {
  background-color: #006dcc;
  background-image: -moz-linear-gradient(top, #0088cc, #0044cc);
  background-image: -ms-linear-gradient(top, #0088cc, #0044cc);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0044cc));
  background-image: -webkit-linear-gradient(top, #0088cc, #0044cc);
  background-image: -o-linear-gradient(top, #0088cc, #0044cc);
  background-image: linear-gradient(top, #0088cc, #0044cc);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#0088cc', endColorstr='#0044cc', GradientType=0);
  border-color: #0044cc #0044cc #002a80;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}
.editableform .datepicker table tr td.active:hover {
  background-color: #006dcc;
  background-image: -moz-linear-gradient(top, #0088cc, #0044cc);
  background-image: -ms-linear-gradient(top, #0088cc, #0044cc);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0044cc));
  background-image: -webkit-linear-gradient(top, #0088cc, #0044cc);
  background-image: -o-linear-gradient(top, #0088cc, #0044cc);
  background-image: linear-gradient(top, #0088cc, #0044cc);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#0088cc', endColorstr='#0044cc', GradientType=0);
  border-color: #0044cc #0044cc #002a80;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  background-color: #0044cc;
}
.editableform .datepicker table tr td.active:hover:hover {
  background-color: #0044cc;
}
.editableform .datepicker table tr td.active:hover:active {
  background-color: #0044cc;
  background-color: #003399 \9;
}
.editableform .datepicker table tr td.active:active {
  background-color: #0044cc;
  background-color: #003399 \9;
}
.editableform .datepicker table tr td.active:hover.active {
  background-color: #0044cc;
  background-color: #003399 \9;
}
.editableform .datepicker table tr td.active:hover.disabled {
  background-color: #0044cc;
}
.editableform .datepicker table tr td.active:hover[disabled] {
  background-color: #0044cc;
}
.editableform .datepicker table tr td.active.disabled {
  background-color: #006dcc;
  background-image: -moz-linear-gradient(top, #0088cc, #0044cc);
  background-image: -ms-linear-gradient(top, #0088cc, #0044cc);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0044cc));
  background-image: -webkit-linear-gradient(top, #0088cc, #0044cc);
  background-image: -o-linear-gradient(top, #0088cc, #0044cc);
  background-image: linear-gradient(top, #0088cc, #0044cc);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#0088cc', endColorstr='#0044cc', GradientType=0);
  border-color: #0044cc #0044cc #002a80;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  background-color: #0044cc;
}
.editableform .datepicker table tr td.active.disabled:hover {
  background-color: #006dcc;
  background-image: -moz-linear-gradient(top, #0088cc, #0044cc);
  background-image: -ms-linear-gradient(top, #0088cc, #0044cc);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0044cc));
  background-image: -webkit-linear-gradient(top, #0088cc, #0044cc);
  background-image: -o-linear-gradient(top, #0088cc, #0044cc);
  background-image: linear-gradient(top, #0088cc, #0044cc);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#0088cc', endColorstr='#0044cc', GradientType=0);
  border-color: #0044cc #0044cc #002a80;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  background-color: #0044cc;
}
.editableform .datepicker table tr td.active.disabled:hover:hover {
  background-color: #0044cc;
}
.editableform .datepicker table tr td.active.disabled:hover:active {
  background-color: #0044cc;
  background-color: #003399 \9;
}
.editableform .datepicker table tr td.active.disabled:active {
  background-color: #0044cc;
  background-color: #003399 \9;
}
.editableform .datepicker table tr td.active.disabled:hover.active {
  background-color: #0044cc;
  background-color: #003399 \9;
}
.editableform .datepicker table tr td.active.disabled:hover.disabled {
  background-color: #0044cc;
}
.editableform .datepicker table tr td.active.disabled:hover[disabled] {
  background-color: #0044cc;
}
.editableform .datepicker table tr td.active.active {
  background-color: #0044cc;
  background-color: #003399 \9;
}
.editableform .datepicker table tr td.active.disabled.active {
  background-color: #0044cc;
  background-color: #003399 \9;
}
.editableform .datepicker table tr td.active.disabled.disabled {
  background-color: #0044cc;
}
.editableform .datepicker table tr td.active[disabled] {
  background-color: #0044cc;
}
.editableform .datepicker table tr td.active.disabled[disabled] {
  background-color: #0044cc;
}
.editableform .datepicker table tr td span {
  display: block;
  width: 23%;
  height: 54px;
  line-height: 54px;
  float: left;
  margin: 1%;
  cursor: pointer;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.editableform .datepicker table tr td span:hover {
  background: #eeeeee;
}
.editableform .datepicker table tr td span.disabled {
  background: none;
  color: #999999;
  cursor: default;
}
.editableform .datepicker table tr td span.disabled:hover {
  background: none;
  color: #999999;
  cursor: default;
}
.editableform .datepicker table tr td span.active {
  background-color: #006dcc;
  background-image: -moz-linear-gradient(top, #0088cc, #0044cc);
  background-image: -ms-linear-gradient(top, #0088cc, #0044cc);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0044cc));
  background-image: -webkit-linear-gradient(top, #0088cc, #0044cc);
  background-image: -o-linear-gradient(top, #0088cc, #0044cc);
  background-image: linear-gradient(top, #0088cc, #0044cc);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#0088cc', endColorstr='#0044cc', GradientType=0);
  border-color: #0044cc #0044cc #002a80;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}
.editableform .datepicker table tr td span.active:hover {
  background-color: #006dcc;
  background-image: -moz-linear-gradient(top, #0088cc, #0044cc);
  background-image: -ms-linear-gradient(top, #0088cc, #0044cc);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0044cc));
  background-image: -webkit-linear-gradient(top, #0088cc, #0044cc);
  background-image: -o-linear-gradient(top, #0088cc, #0044cc);
  background-image: linear-gradient(top, #0088cc, #0044cc);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#0088cc', endColorstr='#0044cc', GradientType=0);
  border-color: #0044cc #0044cc #002a80;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  background-color: #0044cc;
}
.editableform .datepicker table tr td span.active:hover:hover {
  background-color: #0044cc;
}
.editableform .datepicker table tr td span.active:hover:active {
  background-color: #0044cc;
  background-color: #003399 \9;
}
.editableform .datepicker table tr td span.active:active {
  background-color: #0044cc;
  background-color: #003399 \9;
}
.editableform .datepicker table tr td span.active:hover.active {
  background-color: #0044cc;
  background-color: #003399 \9;
}
.editableform .datepicker table tr td span.active:hover.disabled {
  background-color: #0044cc;
}
.editableform .datepicker table tr td span.active:hover[disabled] {
  background-color: #0044cc;
}
.editableform .datepicker table tr td span.active.disabled {
  background-color: #006dcc;
  background-image: -moz-linear-gradient(top, #0088cc, #0044cc);
  background-image: -ms-linear-gradient(top, #0088cc, #0044cc);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0044cc));
  background-image: -webkit-linear-gradient(top, #0088cc, #0044cc);
  background-image: -o-linear-gradient(top, #0088cc, #0044cc);
  background-image: linear-gradient(top, #0088cc, #0044cc);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#0088cc', endColorstr='#0044cc', GradientType=0);
  border-color: #0044cc #0044cc #002a80;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  background-color: #0044cc;
}
.editableform .datepicker table tr td span.active.disabled:hover {
  background-color: #006dcc;
  background-image: -moz-linear-gradient(top, #0088cc, #0044cc);
  background-image: -ms-linear-gradient(top, #0088cc, #0044cc);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0044cc));
  background-image: -webkit-linear-gradient(top, #0088cc, #0044cc);
  background-image: -o-linear-gradient(top, #0088cc, #0044cc);
  background-image: linear-gradient(top, #0088cc, #0044cc);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#0088cc', endColorstr='#0044cc', GradientType=0);
  border-color: #0044cc #0044cc #002a80;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  background-color: #0044cc;
}
.editableform .datepicker table tr td span.active.disabled:hover:hover {
  background-color: #0044cc;
}
.editableform .datepicker table tr td span.active.disabled:hover:active {
  background-color: #0044cc;
  background-color: #003399 \9;
}
.editableform .datepicker table tr td span.active.disabled:active {
  background-color: #0044cc;
  background-color: #003399 \9;
}
.editableform .datepicker table tr td span.active.disabled:hover.active {
  background-color: #0044cc;
  background-color: #003399 \9;
}
.editableform .datepicker table tr td span.active.disabled:hover.disabled {
  background-color: #0044cc;
}
.editableform .datepicker table tr td span.active.disabled:hover[disabled] {
  background-color: #0044cc;
}
.editableform .datepicker table tr td span.active.active {
  background-color: #0044cc;
  background-color: #003399 \9;
}
.editableform .datepicker table tr td span.active.disabled.active {
  background-color: #0044cc;
  background-color: #003399 \9;
}
.editableform .datepicker table tr td span.active.disabled.disabled {
  background-color: #0044cc;
}
.editableform .datepicker table tr td span.active[disabled] {
  background-color: #0044cc;
}
.editableform .datepicker table tr td span.active.disabled[disabled] {
  background-color: #0044cc;
}
.editableform .datepicker table tr td span.old {
  color: #999999;
}
.editableform .datepicker table tr td span.new {
  color: #999999;
}
.editableform .datepicker td {
  text-align: center;
  width: 20px;
  height: 20px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  border: none;
}
.editableform .datepicker th {
  text-align: center;
  width: 20px;
  height: 20px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  border: none;
}
.editableform .datepicker th.datepicker-switch {
  width: 145px;
}
.editableform .datepicker thead tr:first-child th {
  cursor: pointer;
}
.editableform .datepicker thead tr:first-child th:hover {
  background: #eeeeee;
}
.editableform .datepicker thead tr:first-child th.cw {
  cursor: default;
  background-color: transparent;
}
.editableform .datepicker tfoot tr th {
  cursor: pointer;
}
.editableform .datepicker tfoot tr th:hover {
  background: #eeeeee;
}
.editableform .datepicker .cw {
  font-size: 10px;
  width: 12px;
  padding: 0 2px 0 5px;
  vertical-align: middle;
}
.editableform .datepicker-inline {
  width: 220px;
}
.editableform .datepicker.datepicker-rtl {
  direction: rtl;
}
.editableform .datepicker.datepicker-rtl table tr td span {
  float: right;
}
.editableform .datepicker-dropdown {
  top: 0;
  left: 0;
}
.editableform .datepicker-dropdown:before {
  content: '';
  display: inline-block;
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-bottom: 7px solid #ccc;
  border-bottom-color: rgba(0, 0, 0, 0.2);
  position: absolute;
  top: -7px;
  left: 6px;
}
.editableform .datepicker-dropdown:after {
  content: '';
  display: inline-block;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid #ffffff;
  position: absolute;
  top: -6px;
  left: 7px;
}
.editableform .datepicker.days div.datepicker-days {
  display: block;
}
.editableform .datepicker.months div.datepicker-months {
  display: block;
}
.editableform .datepicker.years div.datepicker-years {
  display: block;
}
.table-striped .datepicker table tr td {
  background-color: transparent;
}
.table-striped .datepicker table tr th {
  background-color: transparent;
}
.input-append.date .add-on i {
  display: block;
  cursor: pointer;
  width: 16px;
  height: 16px;
}
.input-prepend.date .add-on i {
  display: block;
  cursor: pointer;
  width: 16px;
  height: 16px;
}
.input-daterange input {
  text-align: center;
}
.input-daterange input:first-child {
  -webkit-border-radius: 3px 0 0 3px;
  -moz-border-radius: 3px 0 0 3px;
  border-radius: 3px 0 0 3px;
}
.input-daterange input:last-child {
  -webkit-border-radius: 0 3px 3px 0;
  -moz-border-radius: 0 3px 3px 0;
  border-radius: 0 3px 3px 0;
}
.input-daterange .add-on {
  display: inline-block;
  width: auto;
  min-width: 16px;
  height: 18px;
  padding: 4px 5px;
  font-weight: normal;
  line-height: 18px;
  text-align: center;
  text-shadow: 0 1px 0 #ffffff;
  vertical-align: middle;
  background-color: #eeeeee;
  border: 1px solid #ccc;
  margin-left: -5px;
  margin-right: -5px;
}
