/*! X-editable - v1.5.1
* In-place editing with Twitter Bootstrap, jQuery UI or pure jQuery
* http://github.com/vitalets/x-editable
* Copyright (c) 2013 <PERSON><PERSON><PERSON>pov; Licensed MIT */
/*
  BS3 width:1005 for inputs breaks editable form in popup
  See: https://github.com/vitalets/x-editable/issues/393
*/
/*for jquery-ui buttons need set height to look more pretty*/
/*add padding for jquery ui*/
/* ---- For specific types ---- */
/* move datepicker icon to center of add-on button. See https://github.com/vitalets/x-editable/issues/183 */
/* checklist vertical alignment */
/* set exact width of textarea to fit buttons toolbar */
/* clear button shown as link in date inputs */
/* IOS-style clear button for text inputs */
/*see https://github.com/vitalets/x-editable/issues/139 */
/*!
 * Datepicker for Bootstrap
 *
 * Copyright 2012 Stefan Petre
 * Improvements by <PERSON>
 * Licensed under the Apache License v2.0
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 */
.editableform {
  margin-bottom: 0;

  .control-group {
    margin-bottom: 0;
    white-space: nowrap;
    line-height: 20px;
  }

  .form-control {
    width: auto;
  }

  .editable-date {
    padding: 0;
    margin: 0;
    float: left;
  }
}

.editable-buttons {
  display: inline-block;
  vertical-align: top;
  margin-left: 7px;
  zoom: 1;
  *display: inline;

  .editable-cancel {
    margin-left: 7px;
  }

  button.ui-button-icon-only {
    height: 24px;
    width: 30px;
  }
}

.editable-buttons.editable-buttons-bottom {
  display: block;
  margin-top: 7px;
  margin-left: 0;
}

.editable-input {
  vertical-align: top;
  display: inline-block;
  width: auto;
  white-space: normal;
  zoom: 1;
  *display: inline;
}

.editableform-loading {
  background: url('../img/loading.gif') center center no-repeat;
  height: 25px;
  width: auto;
  min-width: 25px;
}

.editable-inline {
  .editableform-loading {
    background-position: left 5px;
  }

  .add-on {
    .icon-th {
      margin-top: 3px;
      margin-left: 1px;
    }
  }
}

.editable-error-block {
  max-width: 300px;
  margin: 5px 0 0 0;
  width: auto;
  white-space: normal;
}

.editable-error-block.ui-state-error {
  padding: 3px;
}

.editable-error {
  color: red;
}

.editable-checklist {
  label {
    input[type="checkbox"] {
      vertical-align: middle;
      margin: 0;
    }

    span {
      vertical-align: middle;
      margin: 0;
    }

    white-space: nowrap;
  }
}

.editable-wysihtml5 {
  width: 566px;
  height: 250px;
}

.editable-clear {
  clear: both;
  font-size: 0.9em;
  text-decoration: none;
  text-align: right;
}

.editable-clear-x {
  background: url('../img/clear.png') center center no-repeat;
  display: block;
  width: 13px;
  height: 13px;
  position: absolute;
  opacity: 0.6;
  z-index: 100;
  top: 50%;
  right: 6px;
  margin-top: -6px;

  &:hover {
    opacity: 1;
  }
}

.editable-pre-wrapped {
  white-space: pre-wrap;
}

.editable-container.editable-popup {
  max-width: none !important;
}

.editable-container.popover {
  width: auto;
}

.editable-container.editable-inline {
  display: inline-block;
  vertical-align: middle;
  width: auto;
  zoom: 1;
  *display: inline;
}

.editable-container.ui-widget {
  font-size: inherit;
  z-index: 9990;
}

.editable-click {
  text-decoration: none;
  border-bottom: dashed 1px #0088cc;
}

a.editable-click {
  text-decoration: none;
  border-bottom: dashed 1px #0088cc;

  &:hover {
    text-decoration: none;
    border-bottom: dashed 1px #0088cc;
  }
}

.editable-click.editable-disabled {
  color: #585858;
  cursor: default;
  border-bottom: none;
}

a.editable-click.editable-disabled {
  color: #585858;
  cursor: default;
  border-bottom: none;

  &:hover {
    color: #585858;
    cursor: default;
    border-bottom: none;
  }
}

.editable-empty {
  font-style: italic;
  color: #DD1144;
  text-decoration: none;

  &:hover {
    font-style: italic;
    color: #DD1144;
    text-decoration: none;
  }

  &:focus {
    font-style: italic;
    color: #DD1144;
    text-decoration: none;
  }
}

.editable-unsaved {
  font-weight: bold;
}

.editable-bg-transition {
  -webkit-transition: background-color 1400ms ease-out;
  -moz-transition: background-color 1400ms ease-out;
  -o-transition: background-color 1400ms ease-out;
  -ms-transition: background-color 1400ms ease-out;
  transition: background-color 1400ms ease-out;
}

.form-horizontal {
  .editable {
    padding-top: 5px;
    display: inline-block;
  }
}

.editableform {
  .datepicker {
    padding: 4px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    direction: ltr;

    > div {
      display: none;
    }

    table {
      margin: 0;

      tr {
        td.day {
          &:hover {
            background: #eeeeee;
            cursor: pointer;
          }
        }

        td.old {
          color: #999999;
        }

        td.new {
          color: #999999;
        }

        td.disabled {
          background: none;
          color: #999999;
          cursor: default;

          &:hover {
            background: none;
            color: #999999;
            cursor: default;
          }
        }

        td.today {
          background-color: #fde19a;
          background-image: -moz-linear-gradient(top, #fdd49a, #fdf59a);
          background-image: -ms-linear-gradient(top, #fdd49a, #fdf59a);
          background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#fdd49a), to(#fdf59a));
          background-image: -webkit-linear-gradient(top, #fdd49a, #fdf59a);
          background-image: -o-linear-gradient(top, #fdd49a, #fdf59a);
          background-image: linear-gradient(top, #fdd49a, #fdf59a);
          background-repeat: repeat-x;
          filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fdd49a', endColorstr='#fdf59a', GradientType=0);
          border-color: #fdf59a #fdf59a #fbed50;
          border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
          filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
          color: #000;

          &:hover {
            background-color: #fde19a;
            background-image: -moz-linear-gradient(top, #fdd49a, #fdf59a);
            background-image: -ms-linear-gradient(top, #fdd49a, #fdf59a);
            background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#fdd49a), to(#fdf59a));
            background-image: -webkit-linear-gradient(top, #fdd49a, #fdf59a);
            background-image: -o-linear-gradient(top, #fdd49a, #fdf59a);
            background-image: linear-gradient(top, #fdd49a, #fdf59a);
            background-repeat: repeat-x;
            filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fdd49a', endColorstr='#fdf59a', GradientType=0);
            border-color: #fdf59a #fdf59a #fbed50;
            border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
            filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
            color: #000;
            background-color: #fdf59a;

            &:hover {
              background-color: #fdf59a;
              color: #000;
            }

            &:active {
              background-color: #fdf59a;
              background-color: #fbf069 \9;
            }
          }

          &:active {
            background-color: #fdf59a;
            background-color: #fbf069 \9;
          }

          &:hover.active {
            background-color: #fdf59a;
            background-color: #fbf069 \9;
          }

          &:hover.disabled {
            background-color: #fdf59a;
          }

          &:hover[disabled] {
            background-color: #fdf59a;
          }
        }

        td.today.disabled {
          background-color: #fde19a;
          background-image: -moz-linear-gradient(top, #fdd49a, #fdf59a);
          background-image: -ms-linear-gradient(top, #fdd49a, #fdf59a);
          background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#fdd49a), to(#fdf59a));
          background-image: -webkit-linear-gradient(top, #fdd49a, #fdf59a);
          background-image: -o-linear-gradient(top, #fdd49a, #fdf59a);
          background-image: linear-gradient(top, #fdd49a, #fdf59a);
          background-repeat: repeat-x;
          filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fdd49a', endColorstr='#fdf59a', GradientType=0);
          border-color: #fdf59a #fdf59a #fbed50;
          border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
          filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
          color: #000;
          background-color: #fdf59a;

          &:hover {
            background-color: #fde19a;
            background-image: -moz-linear-gradient(top, #fdd49a, #fdf59a);
            background-image: -ms-linear-gradient(top, #fdd49a, #fdf59a);
            background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#fdd49a), to(#fdf59a));
            background-image: -webkit-linear-gradient(top, #fdd49a, #fdf59a);
            background-image: -o-linear-gradient(top, #fdd49a, #fdf59a);
            background-image: linear-gradient(top, #fdd49a, #fdf59a);
            background-repeat: repeat-x;
            filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fdd49a', endColorstr='#fdf59a', GradientType=0);
            border-color: #fdf59a #fdf59a #fbed50;
            border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
            filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
            color: #000;
            background-color: #fdf59a;

            &:hover {
              background-color: #fdf59a;
            }

            &:active {
              background-color: #fdf59a;
              background-color: #fbf069 \9;
            }
          }

          &:active {
            background-color: #fdf59a;
            background-color: #fbf069 \9;
          }

          &:hover.active {
            background-color: #fdf59a;
            background-color: #fbf069 \9;
          }

          &:hover.disabled {
            background-color: #fdf59a;
          }

          &:hover[disabled] {
            background-color: #fdf59a;
          }
        }

        td.today.active {
          background-color: #fdf59a;
          background-color: #fbf069 \9;

          &:hover {
            color: #fff;
          }
        }

        td.today.disabled.active {
          background-color: #fdf59a;
          background-color: #fbf069 \9;
        }

        td.today.disabled.disabled {
          background-color: #fdf59a;
        }

        td.today[disabled] {
          background-color: #fdf59a;
        }

        td.today.disabled[disabled] {
          background-color: #fdf59a;
        }

        td.range {
          background: #eeeeee;
          -webkit-border-radius: 0;
          -moz-border-radius: 0;
          border-radius: 0;

          &:hover {
            background: #eeeeee;
            -webkit-border-radius: 0;
            -moz-border-radius: 0;
            border-radius: 0;
          }
        }

        td.range.disabled {
          background: #eeeeee;
          -webkit-border-radius: 0;
          -moz-border-radius: 0;
          border-radius: 0;

          &:hover {
            background: #eeeeee;
            -webkit-border-radius: 0;
            -moz-border-radius: 0;
            border-radius: 0;
          }
        }

        td.range.today {
          background-color: #f3d17a;
          background-image: -moz-linear-gradient(top, #f3c17a, #f3e97a);
          background-image: -ms-linear-gradient(top, #f3c17a, #f3e97a);
          background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#f3c17a), to(#f3e97a));
          background-image: -webkit-linear-gradient(top, #f3c17a, #f3e97a);
          background-image: -o-linear-gradient(top, #f3c17a, #f3e97a);
          background-image: linear-gradient(top, #f3c17a, #f3e97a);
          background-repeat: repeat-x;
          filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f3c17a', endColorstr='#f3e97a', GradientType=0);
          border-color: #f3e97a #f3e97a #edde34;
          border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
          filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
          -webkit-border-radius: 0;
          -moz-border-radius: 0;
          border-radius: 0;

          &:hover {
            background-color: #f3d17a;
            background-image: -moz-linear-gradient(top, #f3c17a, #f3e97a);
            background-image: -ms-linear-gradient(top, #f3c17a, #f3e97a);
            background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#f3c17a), to(#f3e97a));
            background-image: -webkit-linear-gradient(top, #f3c17a, #f3e97a);
            background-image: -o-linear-gradient(top, #f3c17a, #f3e97a);
            background-image: linear-gradient(top, #f3c17a, #f3e97a);
            background-repeat: repeat-x;
            filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f3c17a', endColorstr='#f3e97a', GradientType=0);
            border-color: #f3e97a #f3e97a #edde34;
            border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
            filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
            -webkit-border-radius: 0;
            -moz-border-radius: 0;
            border-radius: 0;
            background-color: #f3e97a;

            &:hover {
              background-color: #f3e97a;
            }

            &:active {
              background-color: #f3e97a;
              background-color: #efe24b \9;
            }
          }

          &:active {
            background-color: #f3e97a;
            background-color: #efe24b \9;
          }

          &:hover.active {
            background-color: #f3e97a;
            background-color: #efe24b \9;
          }

          &:hover.disabled {
            background-color: #f3e97a;
          }

          &:hover[disabled] {
            background-color: #f3e97a;
          }
        }

        td.range.today.disabled {
          background-color: #f3d17a;
          background-image: -moz-linear-gradient(top, #f3c17a, #f3e97a);
          background-image: -ms-linear-gradient(top, #f3c17a, #f3e97a);
          background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#f3c17a), to(#f3e97a));
          background-image: -webkit-linear-gradient(top, #f3c17a, #f3e97a);
          background-image: -o-linear-gradient(top, #f3c17a, #f3e97a);
          background-image: linear-gradient(top, #f3c17a, #f3e97a);
          background-repeat: repeat-x;
          filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f3c17a', endColorstr='#f3e97a', GradientType=0);
          border-color: #f3e97a #f3e97a #edde34;
          border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
          filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
          -webkit-border-radius: 0;
          -moz-border-radius: 0;
          border-radius: 0;
          background-color: #f3e97a;

          &:hover {
            background-color: #f3d17a;
            background-image: -moz-linear-gradient(top, #f3c17a, #f3e97a);
            background-image: -ms-linear-gradient(top, #f3c17a, #f3e97a);
            background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#f3c17a), to(#f3e97a));
            background-image: -webkit-linear-gradient(top, #f3c17a, #f3e97a);
            background-image: -o-linear-gradient(top, #f3c17a, #f3e97a);
            background-image: linear-gradient(top, #f3c17a, #f3e97a);
            background-repeat: repeat-x;
            filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f3c17a', endColorstr='#f3e97a', GradientType=0);
            border-color: #f3e97a #f3e97a #edde34;
            border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
            filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
            -webkit-border-radius: 0;
            -moz-border-radius: 0;
            border-radius: 0;
            background-color: #f3e97a;

            &:hover {
              background-color: #f3e97a;
            }

            &:active {
              background-color: #f3e97a;
              background-color: #efe24b \9;
            }
          }

          &:active {
            background-color: #f3e97a;
            background-color: #efe24b \9;
          }

          &:hover.active {
            background-color: #f3e97a;
            background-color: #efe24b \9;
          }

          &:hover.disabled {
            background-color: #f3e97a;
          }

          &:hover[disabled] {
            background-color: #f3e97a;
          }
        }

        td.range.today.active {
          background-color: #f3e97a;
          background-color: #efe24b \9;
        }

        td.range.today.disabled.active {
          background-color: #f3e97a;
          background-color: #efe24b \9;
        }

        td.range.today.disabled.disabled {
          background-color: #f3e97a;
        }

        td.range.today[disabled] {
          background-color: #f3e97a;
        }

        td.range.today.disabled[disabled] {
          background-color: #f3e97a;
        }

        td.selected {
          background-color: #9e9e9e;
          background-image: -moz-linear-gradient(top, #b3b3b3, #808080);
          background-image: -ms-linear-gradient(top, #b3b3b3, #808080);
          background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#b3b3b3), to(#808080));
          background-image: -webkit-linear-gradient(top, #b3b3b3, #808080);
          background-image: -o-linear-gradient(top, #b3b3b3, #808080);
          background-image: linear-gradient(top, #b3b3b3, #808080);
          background-repeat: repeat-x;
          filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#b3b3b3', endColorstr='#808080', GradientType=0);
          border-color: #808080 #808080 #595959;
          border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
          filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
          color: #fff;
          text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);

          &:hover {
            background-color: #9e9e9e;
            background-image: -moz-linear-gradient(top, #b3b3b3, #808080);
            background-image: -ms-linear-gradient(top, #b3b3b3, #808080);
            background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#b3b3b3), to(#808080));
            background-image: -webkit-linear-gradient(top, #b3b3b3, #808080);
            background-image: -o-linear-gradient(top, #b3b3b3, #808080);
            background-image: linear-gradient(top, #b3b3b3, #808080);
            background-repeat: repeat-x;
            filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#b3b3b3', endColorstr='#808080', GradientType=0);
            border-color: #808080 #808080 #595959;
            border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
            filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
            color: #fff;
            text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
            background-color: #808080;

            &:hover {
              background-color: #808080;
            }

            &:active {
              background-color: #808080;
              background-color: #666666 \9;
            }
          }

          &:active {
            background-color: #808080;
            background-color: #666666 \9;
          }

          &:hover.active {
            background-color: #808080;
            background-color: #666666 \9;
          }

          &:hover.disabled {
            background-color: #808080;
          }

          &:hover[disabled] {
            background-color: #808080;
          }
        }

        td.selected.disabled {
          background-color: #9e9e9e;
          background-image: -moz-linear-gradient(top, #b3b3b3, #808080);
          background-image: -ms-linear-gradient(top, #b3b3b3, #808080);
          background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#b3b3b3), to(#808080));
          background-image: -webkit-linear-gradient(top, #b3b3b3, #808080);
          background-image: -o-linear-gradient(top, #b3b3b3, #808080);
          background-image: linear-gradient(top, #b3b3b3, #808080);
          background-repeat: repeat-x;
          filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#b3b3b3', endColorstr='#808080', GradientType=0);
          border-color: #808080 #808080 #595959;
          border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
          filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
          color: #fff;
          text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
          background-color: #808080;

          &:hover {
            background-color: #9e9e9e;
            background-image: -moz-linear-gradient(top, #b3b3b3, #808080);
            background-image: -ms-linear-gradient(top, #b3b3b3, #808080);
            background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#b3b3b3), to(#808080));
            background-image: -webkit-linear-gradient(top, #b3b3b3, #808080);
            background-image: -o-linear-gradient(top, #b3b3b3, #808080);
            background-image: linear-gradient(top, #b3b3b3, #808080);
            background-repeat: repeat-x;
            filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#b3b3b3', endColorstr='#808080', GradientType=0);
            border-color: #808080 #808080 #595959;
            border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
            filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
            color: #fff;
            text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
            background-color: #808080;

            &:hover {
              background-color: #808080;
            }

            &:active {
              background-color: #808080;
              background-color: #666666 \9;
            }
          }

          &:active {
            background-color: #808080;
            background-color: #666666 \9;
          }

          &:hover.active {
            background-color: #808080;
            background-color: #666666 \9;
          }

          &:hover.disabled {
            background-color: #808080;
          }

          &:hover[disabled] {
            background-color: #808080;
          }
        }

        td.selected.active {
          background-color: #808080;
          background-color: #666666 \9;
        }

        td.selected.disabled.active {
          background-color: #808080;
          background-color: #666666 \9;
        }

        td.selected.disabled.disabled {
          background-color: #808080;
        }

        td.selected[disabled] {
          background-color: #808080;
        }

        td.selected.disabled[disabled] {
          background-color: #808080;
        }

        td.active {
          background-color: #006dcc;
          background-image: -moz-linear-gradient(top, #0088cc, #0044cc);
          background-image: -ms-linear-gradient(top, #0088cc, #0044cc);
          background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0044cc));
          background-image: -webkit-linear-gradient(top, #0088cc, #0044cc);
          background-image: -o-linear-gradient(top, #0088cc, #0044cc);
          background-image: linear-gradient(top, #0088cc, #0044cc);
          background-repeat: repeat-x;
          filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#0088cc', endColorstr='#0044cc', GradientType=0);
          border-color: #0044cc #0044cc #002a80;
          border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
          filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
          color: #fff;
          text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);

          &:hover {
            background-color: #006dcc;
            background-image: -moz-linear-gradient(top, #0088cc, #0044cc);
            background-image: -ms-linear-gradient(top, #0088cc, #0044cc);
            background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0044cc));
            background-image: -webkit-linear-gradient(top, #0088cc, #0044cc);
            background-image: -o-linear-gradient(top, #0088cc, #0044cc);
            background-image: linear-gradient(top, #0088cc, #0044cc);
            background-repeat: repeat-x;
            filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#0088cc', endColorstr='#0044cc', GradientType=0);
            border-color: #0044cc #0044cc #002a80;
            border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
            filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
            color: #fff;
            text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
            background-color: #0044cc;

            &:hover {
              background-color: #0044cc;
            }

            &:active {
              background-color: #0044cc;
              background-color: #003399 \9;
            }
          }

          &:active {
            background-color: #0044cc;
            background-color: #003399 \9;
          }

          &:hover.active {
            background-color: #0044cc;
            background-color: #003399 \9;
          }

          &:hover.disabled {
            background-color: #0044cc;
          }

          &:hover[disabled] {
            background-color: #0044cc;
          }
        }

        td.active.disabled {
          background-color: #006dcc;
          background-image: -moz-linear-gradient(top, #0088cc, #0044cc);
          background-image: -ms-linear-gradient(top, #0088cc, #0044cc);
          background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0044cc));
          background-image: -webkit-linear-gradient(top, #0088cc, #0044cc);
          background-image: -o-linear-gradient(top, #0088cc, #0044cc);
          background-image: linear-gradient(top, #0088cc, #0044cc);
          background-repeat: repeat-x;
          filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#0088cc', endColorstr='#0044cc', GradientType=0);
          border-color: #0044cc #0044cc #002a80;
          border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
          filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
          color: #fff;
          text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
          background-color: #0044cc;

          &:hover {
            background-color: #006dcc;
            background-image: -moz-linear-gradient(top, #0088cc, #0044cc);
            background-image: -ms-linear-gradient(top, #0088cc, #0044cc);
            background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0044cc));
            background-image: -webkit-linear-gradient(top, #0088cc, #0044cc);
            background-image: -o-linear-gradient(top, #0088cc, #0044cc);
            background-image: linear-gradient(top, #0088cc, #0044cc);
            background-repeat: repeat-x;
            filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#0088cc', endColorstr='#0044cc', GradientType=0);
            border-color: #0044cc #0044cc #002a80;
            border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
            filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
            color: #fff;
            text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
            background-color: #0044cc;

            &:hover {
              background-color: #0044cc;
            }

            &:active {
              background-color: #0044cc;
              background-color: #003399 \9;
            }
          }

          &:active {
            background-color: #0044cc;
            background-color: #003399 \9;
          }

          &:hover.active {
            background-color: #0044cc;
            background-color: #003399 \9;
          }

          &:hover.disabled {
            background-color: #0044cc;
          }

          &:hover[disabled] {
            background-color: #0044cc;
          }
        }

        td.active.active {
          background-color: #0044cc;
          background-color: #003399 \9;
        }

        td.active.disabled.active {
          background-color: #0044cc;
          background-color: #003399 \9;
        }

        td.active.disabled.disabled {
          background-color: #0044cc;
        }

        td.active[disabled] {
          background-color: #0044cc;
        }

        td.active.disabled[disabled] {
          background-color: #0044cc;
        }

        td {
          span {
            display: block;
            width: 23%;
            height: 54px;
            line-height: 54px;
            float: left;
            margin: 1%;
            cursor: pointer;
            -webkit-border-radius: 4px;
            -moz-border-radius: 4px;
            border-radius: 4px;

            &:hover {
              background: #eeeeee;
            }
          }

          span.disabled {
            background: none;
            color: #999999;
            cursor: default;

            &:hover {
              background: none;
              color: #999999;
              cursor: default;
            }
          }

          span.active {
            background-color: #006dcc;
            background-image: -moz-linear-gradient(top, #0088cc, #0044cc);
            background-image: -ms-linear-gradient(top, #0088cc, #0044cc);
            background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0044cc));
            background-image: -webkit-linear-gradient(top, #0088cc, #0044cc);
            background-image: -o-linear-gradient(top, #0088cc, #0044cc);
            background-image: linear-gradient(top, #0088cc, #0044cc);
            background-repeat: repeat-x;
            filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#0088cc', endColorstr='#0044cc', GradientType=0);
            border-color: #0044cc #0044cc #002a80;
            border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
            filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
            color: #fff;
            text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);

            &:hover {
              background-color: #006dcc;
              background-image: -moz-linear-gradient(top, #0088cc, #0044cc);
              background-image: -ms-linear-gradient(top, #0088cc, #0044cc);
              background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0044cc));
              background-image: -webkit-linear-gradient(top, #0088cc, #0044cc);
              background-image: -o-linear-gradient(top, #0088cc, #0044cc);
              background-image: linear-gradient(top, #0088cc, #0044cc);
              background-repeat: repeat-x;
              filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#0088cc', endColorstr='#0044cc', GradientType=0);
              border-color: #0044cc #0044cc #002a80;
              border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
              filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
              color: #fff;
              text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
              background-color: #0044cc;

              &:hover {
                background-color: #0044cc;
              }

              &:active {
                background-color: #0044cc;
                background-color: #003399 \9;
              }
            }

            &:active {
              background-color: #0044cc;
              background-color: #003399 \9;
            }

            &:hover.active {
              background-color: #0044cc;
              background-color: #003399 \9;
            }

            &:hover.disabled {
              background-color: #0044cc;
            }

            &:hover[disabled] {
              background-color: #0044cc;
            }
          }

          span.active.disabled {
            background-color: #006dcc;
            background-image: -moz-linear-gradient(top, #0088cc, #0044cc);
            background-image: -ms-linear-gradient(top, #0088cc, #0044cc);
            background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0044cc));
            background-image: -webkit-linear-gradient(top, #0088cc, #0044cc);
            background-image: -o-linear-gradient(top, #0088cc, #0044cc);
            background-image: linear-gradient(top, #0088cc, #0044cc);
            background-repeat: repeat-x;
            filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#0088cc', endColorstr='#0044cc', GradientType=0);
            border-color: #0044cc #0044cc #002a80;
            border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
            filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
            color: #fff;
            text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
            background-color: #0044cc;

            &:hover {
              background-color: #006dcc;
              background-image: -moz-linear-gradient(top, #0088cc, #0044cc);
              background-image: -ms-linear-gradient(top, #0088cc, #0044cc);
              background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0044cc));
              background-image: -webkit-linear-gradient(top, #0088cc, #0044cc);
              background-image: -o-linear-gradient(top, #0088cc, #0044cc);
              background-image: linear-gradient(top, #0088cc, #0044cc);
              background-repeat: repeat-x;
              filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#0088cc', endColorstr='#0044cc', GradientType=0);
              border-color: #0044cc #0044cc #002a80;
              border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
              filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
              color: #fff;
              text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
              background-color: #0044cc;

              &:hover {
                background-color: #0044cc;
              }

              &:active {
                background-color: #0044cc;
                background-color: #003399 \9;
              }
            }

            &:active {
              background-color: #0044cc;
              background-color: #003399 \9;
            }

            &:hover.active {
              background-color: #0044cc;
              background-color: #003399 \9;
            }

            &:hover.disabled {
              background-color: #0044cc;
            }

            &:hover[disabled] {
              background-color: #0044cc;
            }
          }

          span.active.active {
            background-color: #0044cc;
            background-color: #003399 \9;
          }

          span.active.disabled.active {
            background-color: #0044cc;
            background-color: #003399 \9;
          }

          span.active.disabled.disabled {
            background-color: #0044cc;
          }

          span.active[disabled] {
            background-color: #0044cc;
          }

          span.active.disabled[disabled] {
            background-color: #0044cc;
          }

          span.old {
            color: #999999;
          }

          span.new {
            color: #999999;
          }
        }
      }
    }

    td {
      text-align: center;
      width: 20px;
      height: 20px;
      -webkit-border-radius: 4px;
      -moz-border-radius: 4px;
      border-radius: 4px;
      border: none;
    }

    th {
      text-align: center;
      width: 20px;
      height: 20px;
      -webkit-border-radius: 4px;
      -moz-border-radius: 4px;
      border-radius: 4px;
      border: none;
    }

    th.datepicker-switch {
      width: 145px;
    }

    thead {
      tr {
        &:first-child {
          th {
            cursor: pointer;

            &:hover {
              background: #eeeeee;
            }
          }

          th.cw {
            cursor: default;
            background-color: transparent;
          }
        }
      }
    }

    tfoot {
      tr {
        th {
          cursor: pointer;

          &:hover {
            background: #eeeeee;
          }
        }
      }
    }

    .cw {
      font-size: 10px;
      width: 12px;
      padding: 0 2px 0 5px;
      vertical-align: middle;
    }
  }

  .datepicker-inline {
    width: 220px;
  }

  .datepicker.datepicker-rtl {
    direction: rtl;

    table {
      tr {
        td {
          span {
            float: right;
          }
        }
      }
    }
  }

  .datepicker-dropdown {
    top: 0;
    left: 0;

    &:before {
      content: '';
      display: inline-block;
      border-left: 7px solid transparent;
      border-right: 7px solid transparent;
      border-bottom: 7px solid #ccc;
      border-bottom-color: rgba(0, 0, 0, 0.2);
      position: absolute;
      top: -7px;
      left: 6px;
    }

    &:after {
      content: '';
      display: inline-block;
      border-left: 6px solid transparent;
      border-right: 6px solid transparent;
      border-bottom: 6px solid #ffffff;
      position: absolute;
      top: -6px;
      left: 7px;
    }
  }

  .datepicker.days {
    div.datepicker-days {
      display: block;
    }
  }

  .datepicker.months {
    div.datepicker-months {
      display: block;
    }
  }

  .datepicker.years {
    div.datepicker-years {
      display: block;
    }
  }
}

.table-striped {
  .datepicker {
    table {
      tr {
        td {
          background-color: transparent;
        }

        th {
          background-color: transparent;
        }
      }
    }
  }
}

.input-append.date {
  .add-on {
    i {
      display: block;
      cursor: pointer;
      width: 16px;
      height: 16px;
    }
  }
}

.input-prepend.date {
  .add-on {
    i {
      display: block;
      cursor: pointer;
      width: 16px;
      height: 16px;
    }
  }
}

.input-daterange {
  input {
    text-align: center;

    &:first-child {
      -webkit-border-radius: 3px 0 0 3px;
      -moz-border-radius: 3px 0 0 3px;
      border-radius: 3px 0 0 3px;
    }

    &:last-child {
      -webkit-border-radius: 0 3px 3px 0;
      -moz-border-radius: 0 3px 3px 0;
      border-radius: 0 3px 3px 0;
    }
  }

  .add-on {
    display: inline-block;
    width: auto;
    min-width: 16px;
    height: 18px;
    padding: 4px 5px;
    font-weight: normal;
    line-height: 18px;
    text-align: center;
    text-shadow: 0 1px 0 #ffffff;
    vertical-align: middle;
    background-color: #eeeeee;
    border: 1px solid #ccc;
    margin-left: -5px;
    margin-right: -5px;
  }
}