{"name": "bootstrap-contextmenu", "main": "bootstrap-contextmenu.js", "version": "0.3.4", "homepage": "https://github.com/sydcanem/bootstrap-contextmenu", "authors": ["sydcanem <<EMAIL>>"], "description": "Context-menu extension for the Bootstrap framework", "keywords": ["boostrap", "contextmenu"], "license": "MIT", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"], "_release": "0.3.4", "_resolution": {"type": "version", "tag": "0.3.4", "commit": "78d4bfd46889c127e3770e00f477a697aa258229"}, "_source": "https://github.com/sydcanem/bootstrap-contextmenu.git", "_target": "0.3.4", "_originalSource": "bootstrap-contextmenu"}