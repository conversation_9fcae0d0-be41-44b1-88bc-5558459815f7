{"version": 3, "sources": ["../../../js/i18n/defaults-ru_RU.js"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;AAChB,EAAE,EAAE,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,IAAI,gBAAgB,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,SAAS,CAAC;AAC3C,IAAI,eAAe,CAAC,CAAC,WAAW,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;AAClD,IAAI,iBAAiB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;AAC7C,IAAI,cAAc,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC;AACjI,IAAI,cAAc,CAAC,CAAC,UAAU,CAAC;AAC/B,IAAI,aAAa,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC;AAClC,IAAI,eAAe,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC;AACrC,IAAI,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5B,EAAE,EAAE,CAAC;AACL,GAAG,MAAM,EAAE,CAAC", "file": "defaults-ru_RU.js", "sourcesContent": ["(function ($) {\r\n  $.fn.selectpicker.defaults = {\r\n    noneSelectedText: 'Ничего не выбрано',\r\n    noneResultsText: 'Совпадений не найдено {0}',\r\n    countSelectedText: 'Вы<PERSON><PERSON><PERSON>н<PERSON> {0} из {1}',\r\n    maxOptionsText: ['Достигнут предел ({n} {var} максимум)', 'Достигнут предел в группе ({n} {var} максимум)', ['шт.', 'шт.']],\r\n    doneButtonText: 'Закрыть',\r\n    selectAllText: 'Выбрать все',\r\n    deselectAllText: 'Отменить все',\r\n    multipleSeparator: ', '\r\n  };\r\n})(jQuery);\r\n"]}