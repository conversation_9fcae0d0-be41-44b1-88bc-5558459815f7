# 智慧派单管理系统 - Docker 部署指南

## 快速部署

### 方法一：使用构建脚本（推荐）

```bash
# 1. 给脚本执行权限
chmod +x docker/build.sh

# 2. 运行构建脚本
./docker/build.sh

# 3. 选择选项 4 (重新构建并启动)
```

### 方法二：使用 Makefile

```bash
# 完整安装（构建 + 启动）
make install

# 或者分步执行
make build    # 构建镜像
make up       # 启动服务
```

### 方法三：使用 Docker Compose

```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps
```

## 访问系统

部署成功后，通过以下地址访问：

- **主应用**: http://localhost:8080
- **数据库管理**: http://localhost:8081 (phpMyAdmin)

### 默认账号

- **管理员**: admin / 123456
- **数据库**: root / 123456

## 常用命令

```bash
# 查看服务状态
make status

# 查看日志
make logs

# 重启服务
make restart

# 停止服务
make stop

# 清理环境
make clean
```

## 生产环境配置

1. 修改默认密码
2. 配置 HTTPS
3. 设置防火墙
4. 配置备份策略

详细信息请参考 `docker/README.md` 和 `ARCHITECTURE.md`。

---

© 2025 智慧派单管理系统
