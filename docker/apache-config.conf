<VirtualHost *:80>
    Server<PERSON>dmin webmaster@localhost
    DocumentRoot /var/www/html/public
    
    <Directory /var/www/html/public>
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
        
        # 启用URL重写
        RewriteEngine On
        
        # 如果请求的文件或目录不存在，则重写到index.php
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^(.*)$ index.php [QSA,L]
    </Directory>
    
    # 日志配置
    ErrorLog ${APACHE_LOG_DIR}/error.log
    CustomLog ${APACHE_LOG_DIR}/access.log combined
    
    # 安全配置
    <Directory /var/www/html/application>
        Require all denied
    </Directory>
    
    <Directory /var/www/html/thinkphp>
        Require all denied
    </Directory>
    
    <Directory /var/www/html/runtime>
        Require all denied
    </Directory>
    
    <Directory /var/www/html/vendor>
        Require all denied
    </Directory>
    
    # PHP配置
    php_value upload_max_filesize 50M
    php_value post_max_size 50M
    php_value max_execution_time 300
    php_value memory_limit 256M
</VirtualHost>
