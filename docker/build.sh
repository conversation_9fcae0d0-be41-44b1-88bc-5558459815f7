#!/bin/bash

# 智慧派单管理系统 Docker 构建脚本
# 作者: 智慧派单团队
# 版本: 1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目信息
PROJECT_NAME="smart-dispatch-system"
IMAGE_NAME="smart-dispatch"
VERSION="1.0.0"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  智慧派单管理系统 Docker 构建工具  ${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo -e "${RED}错误: Docker 未安装，请先安装 Docker${NC}"
    exit 1
fi

# 检查docker-compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}错误: docker-compose 未安装，请先安装 docker-compose${NC}"
    exit 1
fi

# 显示菜单
show_menu() {
    echo -e "${YELLOW}请选择操作:${NC}"
    echo "1. 构建 Docker 镜像"
    echo "2. 启动服务 (docker-compose up)"
    echo "3. 停止服务 (docker-compose down)"
    echo "4. 重新构建并启动"
    echo "5. 查看服务状态"
    echo "6. 查看日志"
    echo "7. 清理镜像和容器"
    echo "8. 退出"
    echo ""
}

# 构建镜像
build_image() {
    echo -e "${BLUE}开始构建 Docker 镜像...${NC}"
    docker build -t ${IMAGE_NAME}:${VERSION} -t ${IMAGE_NAME}:latest .
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ 镜像构建成功！${NC}"
        echo -e "镜像名称: ${IMAGE_NAME}:${VERSION}"
        echo -e "镜像名称: ${IMAGE_NAME}:latest"
    else
        echo -e "${RED}✗ 镜像构建失败！${NC}"
        exit 1
    fi
}

# 启动服务
start_services() {
    echo -e "${BLUE}启动服务...${NC}"
    docker-compose up -d
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ 服务启动成功！${NC}"
        echo ""
        echo -e "${YELLOW}服务访问地址:${NC}"
        echo -e "Web应用: http://localhost:8080"
        echo -e "phpMyAdmin: http://localhost:8081"
        echo ""
        echo -e "${YELLOW}数据库连接信息:${NC}"
        echo -e "主机: localhost"
        echo -e "端口: 3306"
        echo -e "用户名: root"
        echo -e "密码: 123456"
        echo -e "数据库: smart_dispatch"
    else
        echo -e "${RED}✗ 服务启动失败！${NC}"
        exit 1
    fi
}

# 停止服务
stop_services() {
    echo -e "${BLUE}停止服务...${NC}"
    docker-compose down
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ 服务已停止！${NC}"
    else
        echo -e "${RED}✗ 服务停止失败！${NC}"
        exit 1
    fi
}

# 重新构建并启动
rebuild_and_start() {
    echo -e "${BLUE}重新构建并启动服务...${NC}"
    docker-compose down
    docker-compose build --no-cache
    docker-compose up -d
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ 服务重新构建并启动成功！${NC}"
        echo ""
        echo -e "${YELLOW}服务访问地址:${NC}"
        echo -e "Web应用: http://localhost:8080"
        echo -e "phpMyAdmin: http://localhost:8081"
    else
        echo -e "${RED}✗ 服务重新构建失败！${NC}"
        exit 1
    fi
}

# 查看服务状态
show_status() {
    echo -e "${BLUE}服务状态:${NC}"
    docker-compose ps
}

# 查看日志
show_logs() {
    echo -e "${BLUE}查看日志 (按 Ctrl+C 退出):${NC}"
    docker-compose logs -f
}

# 清理镜像和容器
cleanup() {
    echo -e "${YELLOW}警告: 这将删除所有相关的容器、镜像和数据卷！${NC}"
    read -p "确定要继续吗? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${BLUE}清理中...${NC}"
        docker-compose down -v
        docker rmi ${IMAGE_NAME}:${VERSION} ${IMAGE_NAME}:latest 2>/dev/null || true
        docker system prune -f
        echo -e "${GREEN}✓ 清理完成！${NC}"
    else
        echo -e "${YELLOW}取消清理操作${NC}"
    fi
}

# 主循环
while true; do
    show_menu
    read -p "请输入选项 (1-8): " choice
    echo ""
    
    case $choice in
        1)
            build_image
            ;;
        2)
            start_services
            ;;
        3)
            stop_services
            ;;
        4)
            rebuild_and_start
            ;;
        5)
            show_status
            ;;
        6)
            show_logs
            ;;
        7)
            cleanup
            ;;
        8)
            echo -e "${GREEN}感谢使用智慧派单管理系统！${NC}"
            exit 0
            ;;
        *)
            echo -e "${RED}无效选项，请重新选择${NC}"
            ;;
    esac
    
    echo ""
    read -p "按回车键继续..."
    echo ""
done
