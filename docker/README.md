# 智慧派单管理系统 Docker 部署指南

## 概述

本文档介绍如何使用 Docker 部署智慧派单管理系统。系统采用容器化部署方式，包含以下服务：

- **Web应用**: 基于 PHP 8.1 + Apache 的主应用
- **MySQL**: 数据库服务
- **Redis**: 缓存服务（可选）
- **phpMyAdmin**: 数据库管理工具（可选）

## 系统要求

- Docker >= 20.10
- Docker Compose >= 1.29
- 至少 2GB 可用内存
- 至少 5GB 可用磁盘空间

## 快速开始

### 1. 使用构建脚本（推荐）

```bash
# 给脚本执行权限
chmod +x docker/build.sh

# 运行构建脚本
./docker/build.sh
```

脚本提供以下功能：
- 构建 Docker 镜像
- 启动/停止服务
- 查看服务状态和日志
- 清理镜像和容器

### 2. 手动部署

```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 服务访问

部署成功后，可以通过以下地址访问：

- **主应用**: http://localhost:8080
- **phpMyAdmin**: http://localhost:8081
- **MySQL**: localhost:3306
- **Redis**: localhost:6379

## 默认账号信息

### 系统管理员
- 用户名: `admin`
- 密码: `123456`

### 数据库
- 主机: `mysql` (容器内) / `localhost` (宿主机)
- 端口: `3306`
- 用户名: `root`
- 密码: `123456`
- 数据库: `smart_dispatch`

## 目录结构

```
docker/
├── apache-config.conf    # Apache 配置文件
├── mysql/
│   └── init.sql         # 数据库初始化脚本
├── build.sh             # 构建脚本
└── README.md            # 本文档
```

## 环境变量配置

可以通过修改 `docker-compose.yml` 中的环境变量来配置系统：

```yaml
environment:
  - APP_DEBUG=false                    # 调试模式
  - DATABASE_TYPE=mysql               # 数据库类型
  - DATABASE_HOSTNAME=mysql           # 数据库主机
  - DATABASE_DATABASE=smart_dispatch  # 数据库名
  - DATABASE_USERNAME=root            # 数据库用户名
  - DATABASE_PASSWORD=123456          # 数据库密码
  - DATABASE_PREFIX=fa_               # 表前缀
```

## 数据持久化

系统使用 Docker 卷来持久化重要数据：

- `mysql_data`: MySQL 数据文件
- `redis_data`: Redis 数据文件
- `./public/uploads`: 上传文件目录
- `./runtime`: 运行时文件目录

## 常用命令

### 服务管理
```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f [service_name]
```

### 镜像管理
```bash
# 构建镜像
docker-compose build

# 强制重新构建
docker-compose build --no-cache

# 拉取最新镜像
docker-compose pull
```

### 数据管理
```bash
# 备份数据库
docker exec smart-dispatch-mysql mysqldump -u root -p123456 smart_dispatch > backup.sql

# 恢复数据库
docker exec -i smart-dispatch-mysql mysql -u root -p123456 smart_dispatch < backup.sql

# 进入容器
docker exec -it smart-dispatch-web bash
docker exec -it smart-dispatch-mysql bash
```

## 故障排除

### 1. 端口冲突
如果端口被占用，可以修改 `docker-compose.yml` 中的端口映射：

```yaml
ports:
  - "8080:80"  # 改为其他端口，如 "8090:80"
```

### 2. 权限问题
确保以下目录有正确的权限：

```bash
chmod -R 777 runtime/
chmod -R 777 public/uploads/
```

### 3. 数据库连接失败
检查数据库服务是否正常启动：

```bash
docker-compose logs mysql
```

### 4. 内存不足
如果系统内存不足，可以调整 PHP 内存限制：

在 `docker/apache-config.conf` 中修改：
```apache
php_value memory_limit 512M
```

## 生产环境部署

### 1. 安全配置
- 修改默认密码
- 配置 HTTPS
- 限制数据库访问
- 配置防火墙

### 2. 性能优化
- 启用 Redis 缓存
- 配置 CDN
- 优化数据库配置
- 启用 Gzip 压缩

### 3. 监控和日志
- 配置日志轮转
- 设置监控告警
- 定期备份数据

## 更新升级

### 1. 更新应用代码
```bash
# 停止服务
docker-compose down

# 更新代码
git pull

# 重新构建并启动
docker-compose up -d --build
```

### 2. 更新依赖
```bash
# 重新构建镜像
docker-compose build --no-cache

# 启动服务
docker-compose up -d
```

## 技术支持

如有问题，请联系：
- 微信：Aeink_05
- 项目地址：https://gitee.com/one-Zero-Moyuan/smart-dispatch-system

---

© 2025 智慧派单管理系统 - Docker 部署指南
