# 使用官方PHP 8.1 Apache镜像作为基础镜像
FROM php:8.1-apache

# 设置工作目录
WORKDIR /var/www/html

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    libzip-dev \
    zip \
    unzip \
    nodejs \
    npm \
    && docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd zip \
    && docker-php-ext-enable pdo_mysql \
    && a2enmod rewrite \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 安装Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# 复制项目文件
COPY . /var/www/html/

# 设置Apache配置
COPY docker/apache-config.conf /etc/apache2/sites-available/000-default.conf

# 安装PHP依赖
RUN composer install --no-dev --optimize-autoloader

# 安装Node.js依赖并构建前端资源
RUN npm install && npm run build

# 设置文件权限
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html \
    && chmod -R 777 /var/www/html/runtime \
    && chmod -R 777 /var/www/html/public/uploads

# 创建必要的目录
RUN mkdir -p /var/www/html/runtime/log \
    && mkdir -p /var/www/html/runtime/cache \
    && mkdir -p /var/www/html/runtime/temp

# 暴露端口
EXPOSE 80

# 启动Apache
CMD ["apache2-foreground"]
